import { forwardRef, Module } from '@nestjs/common';
import { CategoryController } from './infrastructure/controllers/category.controller';
import { SubcategoryController } from './infrastructure/controllers/subcategory.controller';
import { CategoryRepository } from './infrastructure/repositories/category.repository';
import { SubcategoryRepository } from './infrastructure/repositories/subcategory.repository';
import { GetCategoriesUseCase } from './application/usecases/category/get-categories.usecase';
import { GetCategoryUseCase } from './application/usecases/category/get-category.usecase';
import { CreateCategoryUseCase } from './application/usecases/category/create-category.usecase';
import { UpdateCategoryUseCase } from './application/usecases/category/update-category.usecase';
import { DeleteCategoryUseCase } from './application/usecases/category/delete-category.usecase';
import { GetSubcategoriesUseCase } from './application/usecases/subcategory/get-subcategories.usecase';
import { GetSubcategoryUseCase } from './application/usecases/subcategory/get-subcategory.usecase';
import { CreateSubcategoryUseCase } from './application/usecases/subcategory/create-subcategory.usecase';
import { UpdateSubcategoryUseCase } from './application/usecases/subcategory/update-subcategory.usecase';
import { DeleteSubcategoryUseCase } from './application/usecases/subcategory/delete-subcategory.usecase';
import { GetSubcategoriesByCategoryUseCase } from './application/usecases/subcategory/get-subcategories-by-category.usecase';
import { AuthModule } from '@core/auth/auth.module';
import { PermissionRepository } from '@rbac/infrastructure/repositories/permission.repository';
import { GetCategoriesWithSubcategoriesUseCase } from './application/usecases/category/get-categories-with-subcategories.usecase';
import { WordRepository } from './infrastructure/repositories/word.repository';
import { UserProgressRepository } from './infrastructure/repositories/user-progress.repository';
import { GetSubcategoryDetailsUseCase } from './application/usecases/subcategory/get-subcategory-details.usecase';
import { PracticeController } from './infrastructure/controllers/practice.controller';
import { LearnerProfileController } from './infrastructure/controllers/learner-profile.controller';
import { StartPracticeSessionUseCase } from './application/usecases/practice/start-practice-session.usecase';
import { EndPracticeSessionUseCase } from './application/usecases/practice/end-practice-session.usecase';
import { GetLearnerProfileUseCase } from './application/usecases/learner-profile/get-learner-profile.usecase';
import { UpdateWordProgressUseCase } from './application/usecases/user-progress/update-word-progress.usecase';
import { UpdateLearnerProfileUseCase } from './application/usecases/learner-profile/update-learner-profile.usecase';
import { ExerciseRepository } from './infrastructure/repositories/exercise.repository';
import { LearnerProfileRepository } from './infrastructure/repositories/learner-profile.repository';
import { PracticeSessionRepository } from './infrastructure/repositories/practice-session.repository';
import { GetWordsBySubcategoryUseCase } from './application/usecases/word/get-words-by-subcategory.usecase';
import { GetWordUseCase } from './application/usecases/word/get-word.usecase';
import { GetExercisesBySubcategoryUseCase } from './application/usecases/exercise/get-exercises-by-subcategory.usecase';
import { ExerciseController } from './infrastructure/controllers/excercise.controller';
import { WordController } from './infrastructure/controllers/word.controller';
import { LanguageController } from './infrastructure/controllers/language.controller';
import { GetLanguagesUseCase } from './application/usecases/language/get-languages.usecase';
import { LanguageRepository } from './infrastructure/repositories/language.repository';
import { AchievementsController } from './infrastructure/controllers/achievements.controller';
import { DailyGoalsController } from './infrastructure/controllers/daily-goals.controller';
import { AchievementRepository } from './infrastructure/repositories/achievement.repository';
import { DailyGoalRepository } from './infrastructure/repositories/daily-goal.repository';
import { UserSettingsRepository } from './infrastructure/repositories/user-settings.repository';
import { GetUnlockedAchievementsUseCase } from './application/usecases/achievements/get-unlocked-achievements.usecase';
import { GetCurrentDailyGoalUseCase } from './application/usecases/daily-goals/get-current-daily-goal.usecase';
import { GetRecentAchievementsUseCase } from './application/usecases/achievements/get-recent-achievements.usecase';
import { UserProgressController } from './infrastructure/controllers/user-progress.controller';
import { GetUserProgressBySubcategoryUseCase } from './application/usecases/user-progress/get-user-progress-by-subcategory.usecase';


@Module({
  imports: [
    forwardRef(() => AuthModule), // Usar forwardRef para evitar dependencias circulares
  ],
  controllers: [
    LanguageController,
    CategoryController,
    SubcategoryController,
    WordController,
    ExerciseController,
    PracticeController,
    LearnerProfileController,
    AchievementsController,
    DailyGoalsController,
    UserProgressController
  ],
  providers: [
    // Category use cases
    GetCategoriesUseCase,
    GetCategoryUseCase,
    CreateCategoryUseCase,
    UpdateCategoryUseCase,
    DeleteCategoryUseCase,
    GetCategoriesWithSubcategoriesUseCase,

    // Subcategory use cases
    GetSubcategoriesUseCase,
    GetSubcategoryUseCase,
    CreateSubcategoryUseCase,
    UpdateSubcategoryUseCase,
    DeleteSubcategoryUseCase,
    GetSubcategoriesByCategoryUseCase,
    GetSubcategoryDetailsUseCase,

    GetLanguagesUseCase,
    GetExercisesBySubcategoryUseCase,
    StartPracticeSessionUseCase,
    EndPracticeSessionUseCase,
    UpdateWordProgressUseCase,
    GetLearnerProfileUseCase,
    UpdateLearnerProfileUseCase,

    // Word use cases
    GetWordsBySubcategoryUseCase,
    GetWordUseCase,

    GetUnlockedAchievementsUseCase,
    GetRecentAchievementsUseCase,
    GetCurrentDailyGoalUseCase,
    GetUserProgressBySubcategoryUseCase,

    // Repositories
    {
      provide: 'ICategoryRepository',
      useClass: CategoryRepository,
    },
    {
      provide: 'ISubcategoryRepository',
      useClass: SubcategoryRepository,
    },
    {
      provide: 'IPermissionRepository',
      useClass: PermissionRepository,
    },
    {
      provide: 'IWordRepository',
      useClass: WordRepository,
    },
    {
      provide: 'IUserProgressRepository',
      useClass: UserProgressRepository,
    },

    {
      provide: 'ILanguageRepository',
      useClass: LanguageRepository,
    },
    {
      provide: 'IExerciseRepository',
      useClass: ExerciseRepository,
    },
    {
      provide: 'ILearnerProfileRepository',
      useClass: LearnerProfileRepository,
    },
    {
      provide: 'IPracticeSessionRepository',
      useClass: PracticeSessionRepository,
    },

    {
      provide: 'IAchievementRepository',
      useClass: AchievementRepository,
    },
    {
      provide: 'IDailyGoalRepository',
      useClass: DailyGoalRepository,
    },
    {
      provide: 'IUserSettingsRepository',
      useClass: UserSettingsRepository,
    },
  ],
  exports: [
    'ICategoryRepository',
    'ISubcategoryRepository',
  ]
})
export class LearningModule { }
import { PracticeSession } from '../entities/practice-session-entity';

export interface IPracticeSessionRepository {
  findAll(): Promise<PracticeSession[]>;
  findById(id: number): Promise<PracticeSession | null>;
  findByUser(userId: number): Promise<PracticeSession[]>;
  create(practiceSession: Omit<PracticeSession, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<PracticeSession>;
  update(id: number, practiceSession: Partial<Omit<PracticeSession, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<PracticeSession | null>;
  delete(id: number): Promise<boolean>;
}
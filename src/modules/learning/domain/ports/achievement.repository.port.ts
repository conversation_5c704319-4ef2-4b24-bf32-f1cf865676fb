import { Achievement } from '../entities/achievement.entity';

export interface IAchievementRepository {
  findAll(): Promise<Achievement[]>;
  findById(id: number): Promise<Achievement | null>;
  findActive(): Promise<Achievement[]>;
  findUnlockedByUser(userId: number, since?: Date): Promise<Achievement[]>;
  create(achievement: Omit<Achievement, 'id' | 'createdAt' | 'updatedAt'>): Promise<Achievement>;
  update(id: number, achievement: Partial<Achievement>): Promise<Achievement>;
  delete(id: number): Promise<void>;
}
import { LearnerProfile } from '../entities/learner-profile-entity';

export interface ILearnerProfileRepository {
  findAll(): Promise<LearnerProfile[]>;
  findById(id: number): Promise<LearnerProfile | null>;
  findByUserId(userId: number): Promise<LearnerProfile | null>;
  create(learnerProfile: Omit<LearnerProfile, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<LearnerProfile>;
  update(id: number, learnerProfile: Partial<Omit<LearnerProfile, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<LearnerProfile | null>;
  delete(id: number): Promise<boolean>;
}
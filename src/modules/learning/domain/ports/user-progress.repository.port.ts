import { UserProgress } from '../entities/user-progress.entity';

export interface IUserProgressRepository {
  findAll(): Promise<UserProgress[]>;
  findById(id: number): Promise<UserProgress | null>;
  findByUser(userId: number): Promise<UserProgress[]>;
  findByUserAndWord(userId: number, wordId: number): Promise<UserProgress | null>;
  findByUserAndWords(userId: number, wordIds: number[]): Promise<UserProgress[]>;
  create(userProgress: Omit<UserProgress, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<UserProgress>;
  update(id: number, userProgress: Partial<Omit<UserProgress, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<UserProgress | null>;
  delete(id: number): Promise<boolean>;
  getProgressByUserAndSubcategory(userId: number, subcategoryId: number): Promise<number>;
}
import { Word } from "../entities/word-entity";


export interface IWordRepository {
  findAll(): Promise<Word[]>;
  findById(id: number): Promise<Word | null>;
  findBySubcategory(subcategoryId: number): Promise<Word[]>;
  create(word: Omit<Word, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Word>;
  update(id: number, word: Partial<Omit<Word, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Word | null>;
  delete(id: number): Promise<boolean>;
  countByCategoryId(categoryId: number): Promise<number>;
  countBySubcategoryId(subcategoryId: number): Promise<number>;
  findBySubcategoryId(subcategoryId: number): Promise<Word[]>;
}
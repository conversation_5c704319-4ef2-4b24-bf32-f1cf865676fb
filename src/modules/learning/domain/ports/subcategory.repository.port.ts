import { Subcategory } from '../entities/subcategory.entity';

export interface ISubcategoryRepository {
  findAll(): Promise<Subcategory[]>;
  findById(id: number): Promise<Subcategory | null>;
  create(subcategory: Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Subcategory>;
  update(id: number, subcategory: Partial<Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Subcategory | null>;
  delete(id: number): Promise<boolean>;
  findByCategory(categoryId: number): Promise<Subcategory[]>;
  findActive(): Promise<Subcategory[]>;
}
import { Exercise } from '../entities/exercise.entity';

export interface IExerciseRepository {
  findAll(): Promise<Exercise[]>;
  findById(id: number): Promise<Exercise | null>;
  findByWordId(wordId: number): Promise<Exercise[]>;
  findBySubcategoryId(subcategoryId: number): Promise<Exercise[]>;
  create(exercise: Omit<Exercise, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Exercise>;
  update(id: number, exercise: Partial<Omit<Exercise, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Exercise | null>;
  delete(id: number): Promise<boolean>;
}
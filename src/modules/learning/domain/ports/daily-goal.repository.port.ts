import { DailyGoal } from '../entities/daily-goal.entity';

export interface IDailyGoalRepository {
  findAll(): Promise<DailyGoal[]>;
  findById(id: number): Promise<DailyGoal | null>;
  findByUserId(userId: number): Promise<DailyGoal[]>;
  findByUserAndDate(userId: number, date: Date): Promise<DailyGoal | null>;
  create(dailyGoal: Omit<DailyGoal, 'id' | 'createdAt' | 'updatedAt'>): Promise<DailyGoal>;
  update(id: number, dailyGoal: Partial<DailyGoal>): Promise<DailyGoal>;
  delete(id: number): Promise<void>;
}
import { Language } from "../entities/language.entity";


export interface ILanguageRepository {
  findAll(): Promise<Language[]>;
  findById(id: number): Promise<Language | null>;
  findByCode(code: string): Promise<Language | null>;
  create(language: Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Language>;
  update(id: number, language: Partial<Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Language | null>;
  delete(id: number): Promise<boolean>;
}
import { Category } from '../entities/category.entity';

export interface ICategoryRepository {
  findAll(): Promise<Category[]>;
  findById(id: number): Promise<Category | null>;
  create(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Category>;
  update(id: number, category: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Category | null>;
  delete(id: number): Promise<boolean>;
  findActive(): Promise<Category[]>;
}
export class Category {
    constructor(
      public readonly name: string,
      public readonly description?: string,
      public readonly iconUrl?: string,
      public readonly order: number = 0,
      public readonly isActive: boolean = true,
      public readonly createdAt: Date = new Date(),
      public readonly updatedAt?: Date,
      public readonly deletedAt?: Date,
      public readonly id?: number,
    ) {}subcategoryId
  
    // Método estático para crear una instancia desde un objeto de parámetros
    static fromObject(params: {
      id?: number;
      name: string;
      description?: string;
      iconUrl?: string;
      order?: number;
      isActive?: boolean;
      createdAt?: Date;
      updatedAt?: Date;
      deletedAt?: Date;
    }): Category {
      return new Category(
        params.name,
        params.description,
        params.iconUrl,
        params.order ?? 0,
        params.isActive ?? true,
        params.createdAt ?? new Date(),
        params.updatedAt,
        params.deletedAt,
        params.id
      );
    }
  }
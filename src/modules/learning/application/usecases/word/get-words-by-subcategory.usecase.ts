import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IWordRepository } from '../../../domain/ports/word.repository.port';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { IUserProgressRepository } from '../../../domain/ports/user-progress.repository.port';

@Injectable()
export class GetWordsBySubcategoryUseCase {
  constructor(
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
    
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('IUserProgressRepository')
    private readonly userProgressRepository: IUserProgressRepository,
  ) {}

  async execute(subcategoryId: number, userId: number) {
    // Verificar que la subcategoría existe
    const subcategory = await this.subcategoryRepository.findById(subcategoryId);
    if (!subcategory) {
      throw new NotFoundException(`Subcategoría con ID ${subcategoryId} no encontrada`);
    }
    
    // Obtener todas las palabras de la subcategoría
    const words = await this.wordRepository.findBySubcategory(subcategoryId);
    
    // Obtener el progreso del usuario para estas palabras
    const wordIds = words.map(word => word.id!);
    const userProgress = await this.userProgressRepository.findByUserAndWords(userId, wordIds);
    
    // Mapear el progreso a cada palabra
    const wordsWithProgress = words.map(word => {
      const progress = userProgress.find(p => p.wordId === word.id);
      return {
        ...word,
        progress: progress ? {
          learnedPercentage: progress.learnedPercentage,
          strengthLevel: progress.strengthLevel,
          lastReviewDate: progress.lastReviewDate,
        } : {
          learnedPercentage: 0,
          strengthLevel: 0,
          lastReviewDate: null,
        }
      };
    });
    
    return {
      message: 'Palabras obtenidas con éxito',
      data: wordsWithProgress
    };
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IWordRepository } from '../../../domain/ports/word.repository.port';
import { IUserProgressRepository } from '../../../domain/ports/user-progress.repository.port';
import { IExerciseRepository } from '../../../domain/ports/exercise.repository.port';

@Injectable()
export class GetWordUseCase {
  constructor(
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
    
    @Inject('IUserProgressRepository')
    private readonly userProgressRepository: IUserProgressRepository,
    
    @Inject('IExerciseRepository')
    private readonly exerciseRepository: IExerciseRepository,
  ) {}

  async execute(wordId: number, userId: number) {
    // Obtener la palabra
    const word = await this.wordRepository.findById(wordId);
    if (!word) {
      throw new NotFoundException(`Palabra con ID ${wordId} no encontrada`);
    }
    
    // Obtener el progreso del usuario para esta palabra
    const progress = await this.userProgressRepository.findByUserAndWord(userId, wordId);
    
    // Obtener los ejercicios relacionados con esta palabra
    const exercises = await this.exerciseRepository.findByWordId(wordId);
    
    return {
      message: 'Palabra obtenida con éxito',
      data: {
        ...word,
        progress: progress ? {
          learnedPercentage: progress.learnedPercentage,
          strengthLevel: progress.strengthLevel,
          lastReviewDate: progress.lastReviewDate,
          nextReviewDate: progress.nextReviewDate,
          correctAttempts: progress.correctAttempts,
          totalAttempts: progress.totalAttempts,
        } : {
          learnedPercentage: 0,
          strengthLevel: 0,
          lastReviewDate: null,
          nextReviewDate: null,
          correctAttempts: 0,
          totalAttempts: 0,
        },
        exercises: exercises
      }
    };
  }
}
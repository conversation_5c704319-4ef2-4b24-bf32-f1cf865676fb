import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';
import { UpdateCategoryDto } from '../../dtos/category/update-category.dto';

@Injectable()
export class UpdateCategoryUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(id: number, updateCategoryDto: UpdateCategoryDto) {
    const updatedCategory = await this.categoryRepository.update(id, updateCategoryDto);
    
    if (!updatedCategory) {
      throw new NotFoundException(`Categoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Categoría actualizada con éxito',
      data: updatedCategory,
    };
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';

@Injectable()
export class GetCategoryUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(id: number) {
    const category = await this.categoryRepository.findById(id);
    
    if (!category) {
      throw new NotFoundException(`Categoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Categoría recuperada con éxito',
      data: category,
    };
  }
}
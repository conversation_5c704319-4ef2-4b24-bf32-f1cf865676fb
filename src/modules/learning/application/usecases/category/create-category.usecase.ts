import { Inject, Injectable } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';
import { CreateCategoryDto } from '../../dtos/category/create-category.dto';
import { Category } from '../../../domain/entities/category.entity';

@Injectable()
export class CreateCategoryUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(createCategoryDto: CreateCategoryDto) {
    const newCategory = new Category(
      createCategoryDto.name,
      createCategoryDto.description,
      createCategoryDto.iconUrl,
      createCategoryDto.order,
      createCategoryDto.isActive
    );
    
    const createdCategory = await this.categoryRepository.create(newCategory);
    
    return {
      message: 'Categor<PERSON> creada con éxito',
      data: createdCategory,
    };
  }
}
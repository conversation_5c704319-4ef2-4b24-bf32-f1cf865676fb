import { Inject, Injectable } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { IUserProgressRepository } from '../../../domain/ports/user-progress.repository.port';
import { IWordRepository } from '../../../domain/ports/word.repository.port';

@Injectable()
export class GetCategoriesWithSubcategoriesUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
    
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
    
    @Inject('IUserProgressRepository')
    private readonly userProgressRepository: IUserProgressRepository,
  ) {}

  async execute(userId: number, onlyActive: boolean = true) {
    // Obtener todas las categorías
    const categories = onlyActive 
      ? await this.categoryRepository.findActive() 
      : await this.categoryRepository.findAll();
    
    // Preparar el resultado
    const result: {
      id: number;
      name: string;
      icon?: string;
      wordCount: number;
      subcategories: { id: number; name: string; progress: number }[];
    }[] = [];
    
    // Para cada categoría, obtener sus subcategorías y contar palabras
    for (const category of categories) {
      // Obtener subcategorías de esta categoría
      const subcategories = await this.subcategoryRepository.findByCategory(category.id!);
      
      // Contar palabras en esta categoría
      const wordCount = await this.wordRepository.countByCategoryId(category.id!);
      
      // Preparar el array de subcategorías con progreso
      const subcategoriesWithProgress: { id: number; name: string; progress: number }[] = [];
      
      // Para cada subcategoría, calcular el progreso del usuario
      for (const subcategory of subcategories) {
        if (!onlyActive || subcategory.isActive) {
          // Obtener progreso del usuario para esta subcategoría
          const progress = await this.userProgressRepository.getProgressByUserAndSubcategory(
            userId, 
            subcategory.id!
          );
          
          subcategoriesWithProgress.push({
            id: subcategory.id!,
            name: subcategory.name,
            progress: progress || 0
          });
        }
      }
      
      // Añadir la categoría con sus subcategorías al resultado
      result.push({
        id: category.id!,
        name: category.name,
        icon: category.iconUrl,
        wordCount,
        subcategories: subcategoriesWithProgress
      });
    }
    
    return {
      message: 'Categorías con subcategorías recuperadas con éxito',
      data: result
    };
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';

@Injectable()
export class DeleteCategoryUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(id: number) {
    const deleted = await this.categoryRepository.delete(id);
    
    if (!deleted) {
      throw new NotFoundException(`Categoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Categoría eliminada con éxito',
    };
  }
}
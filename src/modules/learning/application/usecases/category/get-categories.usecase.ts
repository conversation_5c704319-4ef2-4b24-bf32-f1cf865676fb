import { Inject, Injectable } from '@nestjs/common';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';

@Injectable()
export class GetCategoriesUseCase {
  constructor(
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(onlyActive: boolean = false) {
    const categories = onlyActive 
      ? await this.categoryRepository.findActive() 
      : await this.categoryRepository.findAll();
    
    return {
      message: 'Categorías recuperadas con éxito',
      data: categories,
    };
  }
}
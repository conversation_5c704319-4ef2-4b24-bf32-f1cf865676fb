import { Inject, Injectable } from '@nestjs/common';
import { IExerciseRepository } from '../../../domain/ports/exercise.repository.port';

@Injectable()
export class GetExercisesBySubcategoryUseCase {
  constructor(
    @Inject('IExerciseRepository')
    private readonly exerciseRepository: IExerciseRepository,
  ) {}

  async execute(subcategoryId: number) {
    const exercises = await this.exerciseRepository.findBySubcategoryId(subcategoryId);
    
    return {
      message: 'Exercises retrieved successfully',
      data: exercises,
    };
  }
}
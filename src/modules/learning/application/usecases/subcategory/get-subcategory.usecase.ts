import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';

@Injectable()
export class GetSubcategoryUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
  ) {}

  async execute(id: number) {
    const subcategory = await this.subcategoryRepository.findById(id);
    
    if (!subcategory) {
      throw new NotFoundException(`Subcategoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Subcategoría recuperada con éxito',
      data: subcategory,
    };
  }
}
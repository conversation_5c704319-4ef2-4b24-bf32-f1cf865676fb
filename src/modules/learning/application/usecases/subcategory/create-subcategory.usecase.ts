import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';
import { CreateSubcategoryDto } from '../../dtos/subcategory/create-subcategory.dto';
import { Subcategory } from '../../../domain/entities/subcategory.entity';

@Injectable()
export class CreateSubcategoryUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(createSubcategoryDto: CreateSubcategoryDto) {
    // Verificar que la categoría existe
    const category = await this.categoryRepository.findById(createSubcategoryDto.categoryId);
    if (!category) {
      throw new NotFoundException(`Categoría con ID ${createSubcategoryDto.categoryId} no encontrada`);
    }
    
    const newSubcategory = new Subcategory(
      createSubcategoryDto.categoryId,
      createSubcategoryDto.name,
      createSubcategoryDto.description,
      createSubcategoryDto.iconUrl,
      createSubcategoryDto.order,
      createSubcategoryDto.isActive
    );
    
    const createdSubcategory = await this.subcategoryRepository.create(newSubcategory);
    
    return {
      message: 'Subcategoría creada con éxito',
      data: createdSubcategory,
    };
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { IWordRepository } from '../../../domain/ports/word.repository.port';

@Injectable()
export class GetSubcategoryDetailsUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
  ) {}

  async execute(subcategoryId: number) {
    // Verificar que la subcategoría existe
    const subcategory = await this.subcategoryRepository.findById(subcategoryId);
    
    if (!subcategory) {
      throw new NotFoundException(`Subcategoría con ID ${subcategoryId} no encontrada`);
    }
    
    // Obtener las palabras de esta subcategoría
    const words = await this.wordRepository.findBySubcategory(subcategoryId);
    
    // Aquí podríamos obtener las lecciones si tuviéramos un repositorio para ello
    // Por ahora, devolvemos un array vacío
    const lessons = [];
    
    return {
      message: 'Detalles de subcategoría recuperados con éxito',
      data: {
        subcategory,
        words,
        lessons
      }
    };
  }
}
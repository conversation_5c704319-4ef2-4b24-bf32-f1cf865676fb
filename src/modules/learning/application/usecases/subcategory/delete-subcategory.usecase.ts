import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';

@Injectable()
export class DeleteSubcategoryUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
  ) {}

  async execute(id: number) {
    const deleted = await this.subcategoryRepository.delete(id);
    
    if (!deleted) {
      throw new NotFoundException(`Subcategoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Subcategoría eliminada con éxito',
    };
  }
}
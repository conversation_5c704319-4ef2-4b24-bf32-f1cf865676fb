import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';

@Injectable()
export class GetSubcategoriesByCategoryUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(categoryId: number) {
    // Verificar que la categoría existe
    const category = await this.categoryRepository.findById(categoryId);
    if (!category) {
      throw new NotFoundException(`Categoría con ID ${categoryId} no encontrada`);
    }
    
    const subcategories = await this.subcategoryRepository.findByCategory(categoryId);
    
    return {
      message: `Subcategorías de la categoría "${category.name}" recuperadas con éxito`,
      data: subcategories,
    };
  }
}
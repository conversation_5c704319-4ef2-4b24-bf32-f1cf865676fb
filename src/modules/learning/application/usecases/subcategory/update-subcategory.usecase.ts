import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { ICategoryRepository } from '../../../domain/ports/category.repository.port';
import { UpdateSubcategoryDto } from '../../dtos/subcategory/update-subcategory.dto';

@Injectable()
export class UpdateSubcategoryUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('ICategoryRepository')
    private readonly categoryRepository: ICategoryRepository,
  ) {}

  async execute(id: number, updateSubcategoryDto: UpdateSubcategoryDto) {
    // Si se proporciona categoryId, verificar que la categoría existe
    if (updateSubcategoryDto.categoryId) {
      const category = await this.categoryRepository.findById(updateSubcategoryDto.categoryId);
      if (!category) {
        throw new NotFoundException(`Categoría con ID ${updateSubcategoryDto.categoryId} no encontrada`);
      }
    }
    
    const updatedSubcategory = await this.subcategoryRepository.update(id, updateSubcategoryDto);
    
    if (!updatedSubcategory) {
      throw new NotFoundException(`Subcategoría con ID ${id} no encontrada`);
    }
    
    return {
      message: 'Subcategoría actualizada con éxito',
      data: updatedSubcategory,
    };
  }
}
import { Inject, Injectable } from '@nestjs/common';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';

@Injectable()
export class GetSubcategoriesUseCase {
  constructor(
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
  ) {}

  async execute(onlyActive: boolean = false) {
    const subcategories = onlyActive 
      ? await this.subcategoryRepository.findActive() 
      : await this.subcategoryRepository.findAll();
    
    return {
      message: 'Subcategorías recuperadas con éxito',
      data: subcategories,
    };
  }
}
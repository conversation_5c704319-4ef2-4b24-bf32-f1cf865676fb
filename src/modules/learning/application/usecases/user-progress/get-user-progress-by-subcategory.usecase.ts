import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserProgressRepository } from '../../../domain/ports/user-progress.repository.port';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';
import { IWordRepository } from '../../../domain/ports/word.repository.port';

@Injectable()
export class GetUserProgressBySubcategoryUseCase {
  constructor(
    @Inject('IUserProgressRepository')
    private readonly userProgressRepository: IUserProgressRepository,
    
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
    
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
  ) {}

  async execute(userId: number, subcategoryId: number) {
    // Verificar que la subcategoría existe
    const subcategory = await this.subcategoryRepository.findById(subcategoryId);
    if (!subcategory) {
      throw new NotFoundException(`Subcategoría con ID ${subcategoryId} no encontrada`);
    }
    
    // Obtener todas las palabras de la subcategoría
    const words = await this.wordRepository.findBySubcategory(subcategoryId);
    
    // Obtener el progreso del usuario para estas palabras
    const wordIds = words.map(word => word.id!);
    const userProgress = await this.userProgressRepository.findByUserAndWords(userId, wordIds);
    
    // Mapear el progreso a cada palabra
    const wordProgress = userProgress.map(progress => ({
      wordId: progress.wordId,
      learnedPercentage: progress.learnedPercentage,
      strengthLevel: progress.strengthLevel,
      lastReviewDate: progress.lastReviewDate,
    }));
    
    // Calcular el progreso general de la subcategoría
    const overallProgress = await this.userProgressRepository.getProgressByUserAndSubcategory(userId, subcategoryId);
    
    return {
      message: 'Progreso del usuario recuperado con éxito',
      data: {
        userId,
        subcategoryId,
        subcategoryName: subcategory.name,
        overallProgress: overallProgress || 0,
        wordProgress: wordProgress,
        totalWords: words.length,
        learnedWords: wordProgress.filter(p => p.learnedPercentage >= 100).length
      }
    };
  }
}
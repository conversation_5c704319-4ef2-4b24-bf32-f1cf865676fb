import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserProgressRepository } from '../../../domain/ports/user-progress.repository.port';
import { IWordRepository } from '../../../domain/ports/word.repository.port';

@Injectable()
export class UpdateWordProgressUseCase {
  constructor(
    @Inject('IUserProgressRepository')
    private readonly userProgressRepository: IUserProgressRepository,
    
    @Inject('IWordRepository')
    private readonly wordRepository: IWordRepository,
  ) {}

  async execute(userId: number, wordId: number, percentage: number) {
    // Verificar que la palabra existe
    const word = await this.wordRepository.findById(wordId);
    if (!word) {
      throw new NotFoundException(`Palabra con ID ${wordId} no encontrada`);
    }
    
    // Obtener el progreso actual del usuario para esta palabra
    const existingProgress = await this.userProgressRepository.findByUserAndWord(userId, wordId);
    
    if (existingProgress) {
      // Actualizar el progreso existente
      const totalAttempts = existingProgress.totalAttempts + 1;
      const correctAttempts = existingProgress.correctAttempts + (percentage >= 100 ? 1 : 0);
      const newPercentage = Math.max(existingProgress.learnedPercentage, percentage);
      
      const updatedProgress = await this.userProgressRepository.update(existingProgress.id!, {
        learnedPercentage: newPercentage,
        totalAttempts,
        correctAttempts,
        lastReviewDate: new Date(),
        // Calcular la próxima fecha de revisión basada en el nivel de fuerza
        nextReviewDate: this.calculateNextReviewDate(newPercentage),
        // Actualizar el nivel de fuerza basado en el porcentaje de aprendizaje
        strengthLevel: Math.floor(newPercentage / 20), // 0-5 basado en porcentaje 0-100
      });
      
      return {
        message: 'Progreso de palabra actualizado con éxito',
        data: updatedProgress
      };
    } else {
      // Crear un nuevo registro de progreso
      const newProgress = await this.userProgressRepository.create({
        userId,
        wordId,
        learnedPercentage: percentage,
        totalAttempts: 1,
        correctAttempts: percentage >= 100 ? 1 : 0,
        lastReviewDate: new Date(),
        nextReviewDate: this.calculateNextReviewDate(percentage),
        strengthLevel: Math.floor(percentage / 20), // 0-5 basado en porcentaje 0-100
      });
      
      return {
        message: 'Progreso de palabra creado con éxito',
        data: newProgress
      };
    }
  }
  
  private calculateNextReviewDate(percentage: number): Date {
    // Implementar algoritmo de repetición espaciada
    // Por ejemplo, basado en el porcentaje de aprendizaje:
    // - 0-20%: revisar en 1 día
    // - 21-40%: revisar en 3 días
    // - 41-60%: revisar en 7 días
    // - 61-80%: revisar en 14 días
    // - 81-100%: revisar en 30 días
    const now = new Date();
    let daysToAdd = 1;
    
    if (percentage > 80) {
      daysToAdd = 30;
    } else if (percentage > 60) {
      daysToAdd = 14;
    } else if (percentage > 40) {
      daysToAdd = 7;
    } else if (percentage > 20) {
      daysToAdd = 3;
    }
    
    now.setDate(now.getDate() + daysToAdd);
    return now;
  }
}
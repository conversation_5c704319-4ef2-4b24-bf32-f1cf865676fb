import { Inject, Injectable } from '@nestjs/common';
import { ILanguageRepository } from '../../../domain/ports/language.repository.port';

@Injectable()
export class GetLanguagesUseCase {
  constructor(
    @Inject('ILanguageRepository')
    private readonly languageRepository: ILanguageRepository,
  ) {}

  async execute() {
    const languages = await this.languageRepository.findAll();
    
    return {
      message: 'Languages retrieved successfully',
      data: languages,
    };
  }
}
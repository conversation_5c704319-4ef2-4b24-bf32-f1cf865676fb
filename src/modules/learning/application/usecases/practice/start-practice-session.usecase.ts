import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IPracticeSessionRepository } from '../../../domain/ports/practice-session.repository.port';
import { ISubcategoryRepository } from '../../../domain/ports/subcategory.repository.port';

@Injectable()
export class StartPracticeSessionUseCase {
  constructor(
    @Inject('IPracticeSessionRepository')
    private readonly practiceSessionRepository: IPracticeSessionRepository,
    
    @Inject('ISubcategoryRepository')
    private readonly subcategoryRepository: ISubcategoryRepository,
  ) {}

  async execute(userId: number, subcategoryId?: number) {
    // Verificar que la subcategoría existe si se proporciona
    if (subcategoryId) {
      const subcategory = await this.subcategoryRepository.findById(subcategoryId);
      if (!subcategory) {
        throw new NotFoundException(`Subcategoría con ID ${subcategoryId} no encontrada`);
      }
    }
    // Crear una nueva sesión de práctica
    const practiceSession = await this.practiceSessionRepository.create({
      userId,
      subcategoryId: subcategoryId || null,
      startTime: new Date(),
      totalXpEarned: 0,
      correctAnswers: 0,
      totalQuestions: 0,
    });
    
    return {
      message: 'Sesión de práctica iniciada con éxito',
      data: practiceSession
    };
  }
}
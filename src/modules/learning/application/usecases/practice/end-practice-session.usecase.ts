import { Inject, Injectable, NotFoundException } from '@nestjs/common';

import { ILearnerProfileRepository } from '../../../domain/ports/learner-profile.repository.port';
import { IPracticeSessionRepository } from '@modules/learning/domain/ports/practice-session.repository.port';

interface EndPracticeSessionData {
  totalExercises: number;
  correctAnswers: number;
  xpEarned: number;
}

@Injectable()
export class EndPracticeSessionUseCase {
  constructor(
    @Inject('IPracticeSessionRepository')
    private readonly practiceSessionRepository: IPracticeSessionRepository,
    
    @Inject('ILearnerProfileRepository')
    private readonly learnerProfileRepository: ILearnerProfileRepository,
  ) {}

  async execute(sessionId: number, userId: number, data: EndPracticeSessionData) {
    // Verificar que la sesión existe y pertenece al usuario
    const session = await this.practiceSessionRepository.findById(sessionId);
    if (!session) {
      throw new NotFoundException(`Sesión de práctica con ID ${sessionId} no encontrada`);
    }
    
    if (session.userId !== userId) {
      throw new NotFoundException(`Sesión de práctica con ID ${sessionId} no pertenece al usuario`);
    }
    
    // Actualizar la sesión de práctica
    const updatedSession = await this.practiceSessionRepository.update(sessionId, {
      endTime: new Date(),
      totalXpEarned: data.xpEarned,
      correctAnswers: data.correctAnswers,
      totalQuestions: data.totalExercises,
    });
    
    // Actualizar el perfil del aprendiz con la XP ganada
    const learnerProfile = await this.learnerProfileRepository.findByUserId(userId);
    if (learnerProfile) {
      // Actualizar XP total y verificar si sube de nivel
      const newTotalXp = learnerProfile.totalXp + data.xpEarned;
      
      // Actualizar racha si es necesario
      const today = new Date();
      const lastActiveDate = learnerProfile.lastActiveDate;
      
      // Verificar si la última actividad fue ayer
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const isYesterday = 
        lastActiveDate.getDate() === yesterday.getDate() &&
        lastActiveDate.getMonth() === yesterday.getMonth() &&
        lastActiveDate.getFullYear() === yesterday.getFullYear();
      
      let newStreak = learnerProfile.streak;
      if (isYesterday) {
        // Incrementar racha si la última actividad fue ayer
        newStreak += 1;
      } else if (
        lastActiveDate.getDate() !== today.getDate() ||
        lastActiveDate.getMonth() !== today.getMonth() ||
        lastActiveDate.getFullYear() !== today.getFullYear()
      ) {
        // Reiniciar racha si la última actividad no fue hoy ni ayer
        newStreak = 1;
      }
      
      await this.learnerProfileRepository.update(learnerProfile.id!, {
        totalXp: newTotalXp,
        streak: newStreak,
        lastActiveDate: today,
      });
    }
    
    return {
      message: 'Sesión de práctica finalizada con éxito',
      data: updatedSession
    };
  }
}
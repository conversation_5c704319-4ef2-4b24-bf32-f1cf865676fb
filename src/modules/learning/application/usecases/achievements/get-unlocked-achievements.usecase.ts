import { Inject, Injectable } from '@nestjs/common';
import { IAchievementRepository } from '../../../domain/ports/achievement.repository.port';

@Injectable()
export class GetUnlockedAchievementsUseCase {
  constructor(
    @Inject('IAchievementRepository')
    private readonly achievementRepository: IAchievementRepository,
  ) {}

  async execute(userId: number) {
    // Obtener los logros desbloqueados recientemente (últimos 30 días)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const unlockedAchievements = await this.achievementRepository.findUnlockedByUser(
      userId, 
      thirtyDaysAgo
    );
    
    return {
      message: 'Logros desbloqueados obtenidos con éxito',
      data: unlockedAchievements,
    };
  }
}
import { Inject, Injectable } from '@nestjs/common';
import { IAchievementRepository } from '../../../domain/ports/achievement.repository.port';

@Injectable()
export class GetRecentAchievementsUseCase {
  constructor(
    @Inject('IAchievementRepository')
    private readonly achievementRepository: IAchievementRepository,
  ) {}

  async execute() {
    // Obtener los logros más recientes (activos)
    const recentAchievements = await this.achievementRepository.findActive();
    
    // Podríamos limitar a un número específico si es necesario
    const limitedAchievements = recentAchievements.slice(0, 5);
    
    return {
      message: 'Logros recientes obtenidos con éxito',
      data: limitedAchievements,
    };
  }
}
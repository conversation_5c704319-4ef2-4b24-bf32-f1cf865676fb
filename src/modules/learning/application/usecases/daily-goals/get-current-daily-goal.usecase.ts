import { Inject, Injectable } from '@nestjs/common';
import { IDailyGoalRepository } from '../../../domain/ports/daily-goal.repository.port';
import { IUserSettingsRepository } from '../../../domain/ports/user-settings.repository.port';

@Injectable()
export class GetCurrentDailyGoalUseCase {
  constructor(
    @Inject('IDailyGoalRepository')
    private readonly dailyGoalRepository: IDailyGoalRepository,
    
    @Inject('IUserSettingsRepository')
    private readonly userSettingsRepository: IUserSettingsRepository,
  ) {}

  async execute(userId: number) {
    // Obtener la fecha actual (solo año, mes, día)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Buscar la meta diaria actual
    let dailyGoal = await this.dailyGoalRepository.findByUserAndDate(userId, today);
    
    // Si no existe, crear una nueva meta diaria
    if (!dailyGoal) {
      // Obtener la configuración del usuario para saber su meta diaria
      const userSettings = await this.userSettingsRepository.findByUserId(userId);
      const targetXp = userSettings?.dailyGoal || 50; // Valor por defecto si no hay configuración
      
      // Crear la meta diaria
      dailyGoal = await this.dailyGoalRepository.create({
        userId,
        date: today,
        targetXp,
        achievedXp: 0,
        completed: false,
      });
    }
    
    // Calcular el porcentaje de progreso
    const progressPercentage = Math.min(
      Math.round((dailyGoal.achievedXp / dailyGoal.targetXp) * 100),
      100
    );
    
    return {
      message: 'Objetivo diario obtenido con éxito',
      data: {
        ...dailyGoal,
        progressPercentage
      }
    };
  }
}
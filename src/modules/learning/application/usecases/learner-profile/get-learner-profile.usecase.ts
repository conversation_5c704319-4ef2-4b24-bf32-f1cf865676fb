import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ILearnerProfileRepository } from '../../../domain/ports/learner-profile.repository.port';

@Injectable()
export class GetLearnerProfileUseCase {
  constructor(
    @Inject('ILearnerProfileRepository')
    private readonly learnerProfileRepository: ILearnerProfileRepository,
  ) {}

  async execute(userId: number) {
    const learnerProfile = await this.learnerProfileRepository.findByUserId(userId);
    
    if (!learnerProfile) {
      throw new NotFoundException({
        message: 'Learner profile not found',
        data: null,
      });
    }
    
    return {
      message: 'Learner profile retrieved successfully',
      data: learnerProfile,
    };
  }
}
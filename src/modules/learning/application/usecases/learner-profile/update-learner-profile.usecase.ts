import { ILearnerProfileRepository } from '@modules/learning/domain/ports/learner-profile.repository.port';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { UpdateLearnerProfileDto } from '../../dtos/learner-profile/update-learner-profile.dto';


@Injectable()
export class UpdateLearnerProfileUseCase {
  constructor(
    @Inject('ILearnerProfileRepository')
    private readonly learnerProfileRepository: ILearnerProfileRepository,
  ) {}

  async execute(userId: number, updateLearnerProfileDto: UpdateLearnerProfileDto) {
    const learnerProfile = await this.learnerProfileRepository.findByUserId(userId);
    
    if (!learnerProfile) {
      throw new NotFoundException({
        message: 'Learner profile not found',
        data: null,
      });
    }

    if (!learnerProfile.id) {
      throw new NotFoundException({
        message: 'Learner profile ID is missing',
        data: null,
      });
    }
    
    const updatedLearnerProfile = await this.learnerProfileRepository.update(
      learnerProfile.id,
      updateLearnerProfileDto
    );
    
    return {
      message: 'Learner profile updated successfully',
      data: updatedLearnerProfile,
    };
  }
}
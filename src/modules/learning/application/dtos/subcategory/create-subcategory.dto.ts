import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUrl, IsInt, IsBoolean, Min } from 'class-validator';

export class CreateSubcategoryDto {
  @ApiProperty({ description: 'ID de la categoría a la que pertenece', example: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  categoryId: number;

  @ApiProperty({ description: 'Nombre de la subcategoría', example: 'Saludos' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Descripción de la subcategoría', example: 'Saludos y presentaciones básicas' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'URL del icono de la subcategoría', example: '/assets/icons/greetings.png' })
  @IsOptional()
  @IsString()
  iconUrl?: string;

  @ApiPropertyOptional({ description: 'Orden de aparición', example: 1 })
  @IsOptional()
  @IsInt()
  @Min(0)
  order?: number;

  @ApiPropertyOptional({ description: 'Estado de activación', example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
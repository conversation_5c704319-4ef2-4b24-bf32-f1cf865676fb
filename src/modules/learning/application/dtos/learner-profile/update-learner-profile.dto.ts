import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsUrl } from 'class-validator';

export class UpdateLearnerProfileDto {
  @ApiProperty({ required: false, description: 'Nombre del aprendiz' })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty({ required: false, description: 'Apellido del aprendiz' })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty({ required: false, description: 'URL de la foto de perfil' })
  @IsUrl()
  @IsOptional()
  profilePicture?: string;

  @ApiProperty({ required: false, description: 'ID del idioma preferido' })
  @IsNumber()
  @IsOptional()
  preferredLanguageId?: number;
}
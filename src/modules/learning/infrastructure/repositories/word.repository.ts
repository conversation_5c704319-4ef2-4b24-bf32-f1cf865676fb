import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { words, subcategories } from '../../../../database/schemas/learning.schema';
import { eq, and, isNull, count, inArray } from 'drizzle-orm';
import { IWordRepository } from '../../domain/ports/word.repository.port';
import { Word } from '@modules/learning/domain/entities/word-entity';


@Injectable()
export class WordRepository implements IWordRepository {
  async findAll(): Promise<Word[]> {
    const result = await db
      .select()
      .from(words)
      .where(isNull(words.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Word | null> {
    const result = await db
      .select()
      .from(words)
      .where(
        and(
          eq(words.id, id),
          isNull(words.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findBySubcategory(subcategoryId: number): Promise<Word[]> {
    const result = await db
      .select()
      .from(words)
      .where(
        and(
          eq(words.subcategoryId, subcategoryId),
          isNull(words.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  async create(word: Omit<Word, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Word> {
    const result = await db
      .insert(words)
      .values({
        subcategoryId: word.subcategoryId,
        languageId: word.languageId!, 
        originalWord: word.originalWord,
        meaning: word.meaning,
        example: word.example ?? null,
        audioUrl: word.audioUrl ?? null,
        imageUrl: word.imageUrl ?? null,
        difficultyLevel: word.difficultyLevel ?? 1,
        isActive: word.isActive ?? true,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, word: Partial<Omit<Word, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Word | null> {
    const result = await db
      .update(words)
      .set({
        subcategoryId: word.subcategoryId,
        languageId: word.languageId,
        originalWord: word.originalWord,
        meaning: word.meaning,
        example: word.example,
        audioUrl: word.audioUrl,
        imageUrl: word.imageUrl,
        difficultyLevel: word.difficultyLevel,
        isActive: word.isActive,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(words.id, id),
          isNull(words.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(words)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(words.id, id),
          isNull(words.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  async countByCategoryId(categoryId: number): Promise<number> {
    // Primero obtenemos todas las subcategorías de esta categoría
    const subcats = await db
      .select({ id: subcategories.id })
      .from(subcategories)
      .where(
        and(
          eq(subcategories.categoryId, categoryId),
          isNull(subcategories.deletedAt)
        )
      );
    
    // Si no hay subcategorías, devolvemos 0
    if (subcats.length === 0) {
      return 0;
    }
    
    // Obtenemos los IDs de las subcategorías
    const subcategoryIds = subcats.map(s => s.id);
    
    // Contamos las palabras en estas subcategorías
    const result = await db
      .select({ count: count() })
      .from(words)
      .where(
        and(
          inArray(words.subcategoryId, subcategoryIds),
          isNull(words.deletedAt)
        )
      );
    
    return result[0]?.count || 0;
  }

  async countBySubcategoryId(subcategoryId: number): Promise<number> {
    const result = await db
      .select({ count: count() })
      .from(words)
      .where(
        and(
          eq(words.subcategoryId, subcategoryId),
          isNull(words.deletedAt)
        )
      );
    
    return result[0]?.count || 0;
  }

  async findBySubcategoryId(subcategoryId: number): Promise<Word[]> {
    // Este método es un alias de findBySubcategory para mantener la consistencia
    return this.findBySubcategory(subcategoryId);
  }

  private mapToDomain(raw: any): Word {
    return new Word(
        raw.subcategoryId,
        raw.originalWord,  
        raw.meaning,       
        raw.audioUrl,
        raw.imageUrl,
        raw.difficultyLevel, 
        raw.isActive,
        raw.createdAt,
        raw.updatedAt,
        raw.deletedAt,
        raw.id,
    );
  }
}
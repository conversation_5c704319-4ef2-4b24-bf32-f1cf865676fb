import { Injectable } from '@nestjs/common';
import { ICategoryRepository } from '../../domain/ports/category.repository.port';
import { Category } from '../../domain/entities/category.entity';
import { db } from '../../../../database';
import { categories } from '../../../../database/schemas/learning.schema';
import { eq, isNull, and } from 'drizzle-orm';

@Injectable()
export class CategoryRepository implements ICategoryRepository {
  async findAll(): Promise<Category[]> {
    const result = await db
      .select()
      .from(categories)
      .where(isNull(categories.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Category | null> {
    const result = await db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.id, id),
          isNull(categories.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Category> {
    const result = await db
      .insert(categories)
      .values({
        name: category.name,
        description: category.description,
        iconUrl: category.iconUrl,
        order: category.order,
        isActive: category.isActive,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, categoryData: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Category | null> {
    const updateData: any = {
      ...categoryData,
      updatedAt: new Date()
    };
    
    const result = await db
      .update(categories)
      .set(updateData)
      .where(
        and(
          eq(categories.id, id),
          isNull(categories.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(categories)
      .set({ 
        deletedAt: new Date(),
        updatedAt: new Date()
      })
      .where(
        and(
          eq(categories.id, id),
          isNull(categories.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  async findActive(): Promise<Category[]> {
    const result = await db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.isActive, true),
          isNull(categories.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  private mapToDomain(raw: any): Category {
    return new Category(
      raw.name,
      raw.description,
      raw.iconUrl,
      raw.order,
      raw.isActive,
      raw.createdAt,
      raw.updatedAt,
      raw.deletedAt,
      raw.id
    );
  }
}
import { Injectable } from '@nestjs/common';
import { IUserSettingsRepository } from '../../domain/ports/user-settings.repository.port';
import { UserSettings } from '../../domain/entities/user-settings.entity';
import { db } from '../../../../database';
import { userSettings } from '../../../../database/schemas/learning.schema';
import { eq } from 'drizzle-orm';

@Injectable()
export class UserSettingsRepository implements IUserSettingsRepository {
  async findByUserId(userId: number): Promise<UserSettings | null> {
    const result = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, userId));
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(settings: Omit<UserSettings, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserSettings> {
    const result = await db
      .insert(userSettings)
      .values(settings)
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, settings: Partial<UserSettings>): Promise<UserSettings> {
    const result = await db
      .update(userSettings)
      .set(settings)
      .where(eq(userSettings.id, id))
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  private mapToDomain(raw: any): UserSettings {
    return {
      id: raw.id,
      userId: raw.userId,
      dailyGoal: raw.dailyGoal,
      notificationsEnabled: raw.notificationsEnabled,
      soundEnabled: raw.soundEnabled,
      darkMode: raw.darkMode,
      createdAt: raw.createdAt,
      updatedAt: raw.updatedAt
    };
  }
}
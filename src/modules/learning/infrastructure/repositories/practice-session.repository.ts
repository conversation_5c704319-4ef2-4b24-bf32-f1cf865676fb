import { Injectable } from '@nestjs/common';
import { IPracticeSessionRepository } from '../../domain/ports/practice-session.repository.port';
import { PracticeSession } from '../../domain/entities/practice-session-entity';
import { db } from '../../../../database';
import { practiceSessions } from '../../../../database/schemas/learning.schema';
import { and, eq, isNull } from 'drizzle-orm';

@Injectable()
export class PracticeSessionRepository implements IPracticeSessionRepository {
  async findAll(): Promise<PracticeSession[]> {
    const result = await db
      .select()
      .from(practiceSessions)
      .where(isNull(practiceSessions.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<PracticeSession | null> {
    const result = await db
      .select()
      .from(practiceSessions)
      .where(
        and(
          eq(practiceSessions.id, id),
          isNull(practiceSessions.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findByUser(userId: number): Promise<PracticeSession[]> {
    const result = await db
      .select()
      .from(practiceSessions)
      .where(
        and(
          eq(practiceSessions.userId, userId),
          isNull(practiceSessions.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  async create(practiceSession: Omit<PracticeSession, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<PracticeSession> {
    const result = await db
      .insert(practiceSessions)
      .values({
        userId: practiceSession.userId,
        subcategoryId: practiceSession.subcategoryId || null,
        startTime: practiceSession.startTime,
        endTime: practiceSession.endTime || null,
        totalXpEarned: practiceSession.totalXpEarned,
        correctAnswers: practiceSession.correctAnswers,
        totalQuestions: practiceSession.totalQuestions,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, practiceSession: Partial<Omit<PracticeSession, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<PracticeSession | null> {
    const result = await db
      .update(practiceSessions)
      .set({
        subcategoryId: practiceSession.subcategoryId,
        startTime: practiceSession.startTime,
        endTime: practiceSession.endTime,
        totalXpEarned: practiceSession.totalXpEarned,
        correctAnswers: practiceSession.correctAnswers,
        totalQuestions: practiceSession.totalQuestions,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(practiceSessions.id, id),
          isNull(practiceSessions.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(practiceSessions)
      .set({
        deletedAt: new Date(),
      })
      .where(
        and(
          eq(practiceSessions.id, id),
          isNull(practiceSessions.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  private mapToDomain(raw: any): PracticeSession {
    return new PracticeSession(
      raw.userId,
      raw.startTime,
      raw.subcategoryId,
      raw.endTime,
      raw.totalXpEarned,
      raw.correctAnswers,
      raw.totalQuestions,
      raw.createdAt,
      raw.updatedAt,
      raw.deletedAt,
      raw.id
    );
  }
}
import { Injectable } from '@nestjs/common';
import { ISubcategoryRepository } from '../../domain/ports/subcategory.repository.port';
import { Subcategory } from '../../domain/entities/subcategory.entity';
import { db } from '../../../../database';
import { subcategories } from '../../../../database/schemas/learning.schema';
import { eq, isNull,and } from 'drizzle-orm';

@Injectable()
export class SubcategoryRepository implements ISubcategoryRepository {
  async findAll(): Promise<Subcategory[]> {
    const result = await db
      .select()
      .from(subcategories)
      .where(isNull(subcategories.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Subcategory | null> {
    const result = await db
      .select()
      .from(subcategories)
      .where(
        and(
          eq(subcategories.id, id),
          isNull(subcategories.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(subcategory: Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Subcategory> {
    const result = await db
      .insert(subcategories)
      .values({
        categoryId: subcategory.categoryId,
        name: subcategory.name,
        description: subcategory.description,
        iconUrl: subcategory.iconUrl,
        order: subcategory.order,
        isActive: subcategory.isActive,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, subcategoryData: Partial<Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Subcategory | null> {
    const updateData: any = {
      ...subcategoryData,
      updatedAt: new Date()
    };
    
    const result = await db
      .update(subcategories)
      .set(updateData)
      .where(
        and(
          eq(subcategories.id, id),
          isNull(subcategories.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(subcategories)
      .set({ 
        deletedAt: new Date(),
        updatedAt: new Date()
      })
      .where(
        and(
          eq(subcategories.id, id),
          isNull(subcategories.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  async findByCategory(categoryId: number): Promise<Subcategory[]> {
    const result = await db
      .select()
      .from(subcategories)
      .where(
        and(
          eq(subcategories.categoryId, categoryId),
          isNull(subcategories.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  async findActive(): Promise<Subcategory[]> {
    const result = await db
      .select()
      .from(subcategories)
      .where(
        and(
          eq(subcategories.isActive, true),
          isNull(subcategories.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  private mapToDomain(raw: any): Subcategory {
    return new Subcategory(
      raw.categoryId,
      raw.name,
      raw.description,
      raw.iconUrl,
      raw.order,
      raw.isActive,
      raw.createdAt,
      raw.updatedAt,
      raw.deletedAt,
      raw.id
    );
  }
}
import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { userProgress, words, subcategories } from '../../../../database/schemas/learning.schema';
import { eq, and, isNull, avg, inArray } from 'drizzle-orm';

import { UserProgress } from '@modules/learning/domain/entities/user-progress.entity';
import { IUserProgressRepository } from '@modules/learning/domain/ports/user-progress.repository.port';

@Injectable()
export class UserProgressRepository implements IUserProgressRepository {
  async findAll(): Promise<UserProgress[]> {
    const result = await db
      .select()
      .from(userProgress)
      .where(isNull(userProgress.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<UserProgress | null> {
    const result = await db
      .select()
      .from(userProgress)
      .where(
        and(
          eq(userProgress.id, id),
          isNull(userProgress.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }
  async findByUser(userId: number): Promise<UserProgress[]> {
    const result = await db
      .select()
      .from(userProgress)
      .where(eq(userProgress.userId, userId));
    
    return result.map(this.mapToDomain);
  }

  async findByUserAndWord(userId: number, wordId: number): Promise<UserProgress | null> {
    const result = await db
      .select()
      .from(userProgress)
      .where(
        and(
          eq(userProgress.userId, userId),
          eq(userProgress.wordId, wordId)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(progress: Omit<UserProgress, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserProgress> {
    const result = await db
      .insert(userProgress)
      .values({
        userId: progress.userId,
        wordId: progress.wordId,
        learnedPercentage: progress.learnedPercentage,
        lastReviewDate: progress.lastReviewDate,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, progress: Partial<Omit<UserProgress, 'id' | 'createdAt' | 'updatedAt'>>): Promise<UserProgress | null> {
    const result = await db
      .update(userProgress)
      .set({
        learnedPercentage: progress.learnedPercentage,
        lastReviewDate: progress.lastReviewDate || new Date(),
        updatedAt: new Date(),
      })
      .where(eq(userProgress.id, id))
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async getProgressByUserAndSubcategory(userId: number, subcategoryId: number): Promise<number> {
    // Primero obtenemos todas las palabras de esta subcategoría
    const wordsInSubcategory = await db
      .select({ id: words.id })
      .from(words)
      .where(
        and(
          eq(words.subcategoryId, subcategoryId),
          isNull(words.deletedAt)
        )
      );
    
    // Si no hay palabras, devolvemos 0
    if (wordsInSubcategory.length === 0) {
      return 0;
    }
    
    // Obtenemos los IDs de las palabras
    const wordIds = wordsInSubcategory.map(w => w.id);
    
    // Calculamos el progreso promedio
    const result = await db
      .select({ avgProgress: avg(userProgress.learnedPercentage) })
      .from(userProgress)
      .where(
        and(
          eq(userProgress.userId, userId),
          inArray(userProgress.wordId, wordIds)
        )
      );
    
    // Si no hay progreso registrado, devolvemos 0
    return Number(result[0]?.avgProgress) || 0;
  }

  async findByUserAndWords(userId: number, wordIds: number[]): Promise<UserProgress[]> {
    const result = await db
      .select()
      .from(userProgress)
      .where(
        and(
          eq(userProgress.userId, userId),
          inArray(userProgress.wordId, wordIds),
          isNull(userProgress.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(userProgress)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(userProgress.id, id),
          isNull(userProgress.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  private mapToDomain(raw: any): UserProgress {
    return new UserProgress(
      raw.userId,
      raw.wordId,
      raw.learnedPercentage,
      raw.correctAttempts,
      raw.totalAttempts,
      raw.lastReviewDa,
      raw.nextReviewDa,
      raw.strengthLevel,
      raw.createdAt,
      raw.updatedAt,
      raw.id
    );
  }
}

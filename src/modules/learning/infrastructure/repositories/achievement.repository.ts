import { Injectable } from '@nestjs/common';
import { IAchievementRepository } from '../../domain/ports/achievement.repository.port';
import { Achievement } from '../../domain/entities/achievement.entity';
import { db } from '../../../../database';
import { eq, and, gte } from 'drizzle-orm';
import { achievements, userAchievements } from '../../../../database/schemas/learning.schema';

@Injectable()
export class AchievementRepository implements IAchievementRepository {
  async findAll(): Promise<Achievement[]> {
    const result = await db.select().from(achievements);
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Achievement | null> {
    const result = await db
      .select()
      .from(achievements)
      .where(eq(achievements.id, id));
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findActive(): Promise<Achievement[]> {
    const result = await db
      .select()
      .from(achievements)
      .where(eq(achievements.isActive, true));
    
    return result.map(this.mapToDomain);
  }

  async findUnlockedByUser(userId: number, since?: Date): Promise<any[]> {
    // Construir la consulta base
    const baseQuery = db
      .select({
        id: achievements.id,
        name: achievements.name,
        description: achievements.description,
        iconUrl: achievements.iconUrl,
        requiredCondition: achievements.requiredCondition,
        xpReward: achievements.xpReward,
        isActive: achievements.isActive,
        unlockedAt: userAchievements.unlockedAt
      })
      .from(achievements)
      .innerJoin(
        userAchievements,
        and(
          eq(achievements.id, userAchievements.achievementId),
          eq(userAchievements.userId, userId)
        )
      );
    
    // Aplicar filtro de fecha si se proporciona
    const query = since 
      ? baseQuery.where(gte(userAchievements.unlockedAt, since))
      : baseQuery;
    
    const result = await query.orderBy(userAchievements.unlockedAt);
    
    return result.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      iconUrl: item.iconUrl,
      requiredCondition: item.requiredCondition,
      xpReward: item.xpReward,
      isActive: item.isActive,
      unlockedAt: item.unlockedAt
    }));
  }

  async create(achievement: Omit<Achievement, 'id' | 'createdAt' | 'updatedAt'>): Promise<Achievement> {
    const result = await db
      .insert(achievements)
      .values(achievement)
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, achievement: Partial<Achievement>): Promise<Achievement> {
    const result = await db
      .update(achievements)
      .set(achievement)
      .where(eq(achievements.id, id))
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<void> {
    await db
      .delete(achievements)
      .where(eq(achievements.id, id));
  }

  private mapToDomain(raw: any): Achievement {
    return {
      id: raw.id,
      name: raw.name,
      description: raw.description,
      iconUrl: raw.iconUrl,
      requiredCondition: raw.requiredCondition,
      xpReward: raw.xpReward,
      isActive: raw.isActive,
      createdAt: raw.createdAt,
      updatedAt: raw.updatedAt,
      unlockedAt: raw.unlockedAt
    };
  }
}
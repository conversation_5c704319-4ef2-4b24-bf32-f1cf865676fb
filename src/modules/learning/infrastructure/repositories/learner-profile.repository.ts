import { Injectable } from '@nestjs/common';
import { ILearnerProfileRepository } from '../../domain/ports/learner-profile.repository.port';
import { LearnerProfile } from '../../domain/entities/learner-profile-entity';
import { db } from '../../../../database';
import { learnerProfiles } from '../../../../database/schemas/learning.schema';
import { and, eq, isNull } from 'drizzle-orm';

@Injectable()
export class LearnerProfileRepository implements ILearnerProfileRepository {
  async findAll(): Promise<LearnerProfile[]> {
    const result = await db
      .select()
      .from(learnerProfiles)
      .where(isNull(learnerProfiles.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<LearnerProfile | null> {
    const result = await db
      .select()
      .from(learnerProfiles)
      .where(
        and(
          eq(learnerProfiles.id, id),
          isNull(learnerProfiles.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findByUserId(userId: number): Promise<LearnerProfile | null> {
    const result = await db
      .select()
      .from(learnerProfiles)
      .where(
        and(
          eq(learnerProfiles.userId, userId),
          isNull(learnerProfiles.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(learnerProfile: Omit<LearnerProfile, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<LearnerProfile> {
    const result = await db
      .insert(learnerProfiles)
      .values({
        userId: learnerProfile.userId,
        firstName: learnerProfile.firstName || null,
        lastName: learnerProfile.lastName || null,
        profilePicture: learnerProfile.profilePicture || null,
        currentLevel: learnerProfile.currentLevel,
        totalXp: learnerProfile.totalXp,
        streak: learnerProfile.streak,
        lastActiveDate: learnerProfile.lastActiveDate,
        preferredLanguageId: learnerProfile.preferredLanguageId || null,
      })
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, learnerProfile: Partial<Omit<LearnerProfile, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<LearnerProfile | null> {
    const result = await db
      .update(learnerProfiles)
      .set({
        firstName: learnerProfile.firstName,
        lastName: learnerProfile.lastName,
        profilePicture: learnerProfile.profilePicture,
        currentLevel: learnerProfile.currentLevel,
        totalXp: learnerProfile.totalXp,
        streak: learnerProfile.streak,
        lastActiveDate: learnerProfile.lastActiveDate,
        preferredLanguageId: learnerProfile.preferredLanguageId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(learnerProfiles.id, id),
          isNull(learnerProfiles.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(learnerProfiles)
      .set({
        deletedAt: new Date(),
      })
      .where(
        and(
          eq(learnerProfiles.id, id),
          isNull(learnerProfiles.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  private mapToDomain(raw: any): LearnerProfile {
    return new LearnerProfile(
      raw.userId,
      raw.firstName,
      raw.lastName,
      raw.profilePicture,
      raw.currentLevel,
      raw.totalXp,
      raw.streak,
      raw.lastActiveDate,
      raw.preferredLanguageId,
      raw.createdAt,
      raw.updatedAt,
      raw.deletedAt,
      raw.id
    );
  }
}
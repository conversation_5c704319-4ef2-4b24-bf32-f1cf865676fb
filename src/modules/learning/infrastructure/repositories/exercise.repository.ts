import { Injectable } from '@nestjs/common';
import { IExerciseRepository } from '../../domain/ports/exercise.repository.port';
import { Exercise } from '../../domain/entities/exercise.entity';
import { db } from '../../../../database';
import { exercises, words } from '../../../../database/schemas/learning.schema';
import { and, eq, isNull } from 'drizzle-orm';

@Injectable()
export class ExerciseRepository implements IExerciseRepository {
  async findAll(): Promise<Exercise[]> {
    const result = await db
      .select()
      .from(exercises)
      .where(isNull(exercises.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Exercise | null> {
    const result = await db
      .select()
      .from(exercises)
      .where(
        and(
          eq(exercises.id, id),
          isNull(exercises.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findByWordId(wordId: number): Promise<Exercise[]> {
    const result = await db
      .select()
      .from(exercises)
      .where(
        and(
          eq(exercises.wordId, wordId),
          isNull(exercises.deletedAt)
        )
      );
    
    return result.map(this.mapToDomain);
  }

  async findBySubcategoryId(subcategoryId: number): Promise<Exercise[]> {
    // Para encontrar ejercicios por subcategoría, necesitamos unir con la tabla de palabras
    const result = await db
      .select({
        exercise: exercises,
        word: words,
      })
      .from(exercises)
      .innerJoin(words, eq(exercises.wordId, words.id))
      .where(
        and(
          eq(words.subcategoryId, subcategoryId),
          isNull(exercises.deletedAt),
          isNull(words.deletedAt)
        )
      );
    
    return result.map(item => this.mapToDomain(item.exercise));
  }

  async create(exerciseData: Omit<Exercise, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Exercise> {
    // Asegurarse de que todos los campos requeridos estén presentes y con el tipo correcto
    const valuesToInsert = {
      wordId: exerciseData.wordId,
      type: exerciseData.type,
      question: exerciseData.question,
      correctAnswer: exerciseData.correctAnswer,
      options: exerciseData.options ? JSON.stringify(exerciseData.options) : null,
      hint: exerciseData.hint,
      points: exerciseData.points,
      timeLimit: exerciseData.timeLimit,
      isActive: exerciseData.isActive ?? true,
    };
  
    const result = await db
      .insert(exercises)
      .values(valuesToInsert as any)
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, exerciseData: Partial<Omit<Exercise, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Exercise | null> {
    const result = await db
      .update(exercises)
      .set({
        wordId: exerciseData.wordId,
        type: exerciseData.type as any,
        question: exerciseData.question,
        correctAnswer: exerciseData.correctAnswer,
        options: exerciseData.options,
        hint: exerciseData.hint,
        points: exerciseData.points,
        timeLimit: exerciseData.timeLimit,
        isActive: exerciseData.isActive,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(exercises.id, id),
          isNull(exercises.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(exercises)
      .set({
        deletedAt: new Date(),
      })
      .where(
        and(
          eq(exercises.id, id),
          isNull(exercises.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }

  private mapToDomain(raw: any): Exercise {
    return new Exercise(
      raw.wordId,
      raw.type,
      raw.question,
      raw.correctAnswer,
      raw.options,
      raw.hint,
      raw.points,
      raw.timeLimit,
      raw.isActive,
      raw.createdAt,
      raw.updatedAt,
      raw.deletedAt,
      raw.id
    );
  }
}
import { Injectable } from '@nestjs/common';
import { ILanguageRepository } from '../../domain/ports/language.repository.port';
import { Language } from '../../domain/entities/language.entity';
import { db } from '../../../../database';
import { languages } from '../../../../database/schemas/learning.schema';
import { and, eq, isNull } from 'drizzle-orm';

@Injectable()
export class LanguageRepository implements ILanguageRepository {
  private mapToDomain(languageData: any): Language {
    return new Language(
      languageData.name,
      languageData.code,
      languageData.nativeName,
      languageData.flagUrl,
      languageData.isActive,
      languageData.createdAt,
      languageData.updatedAt,
      languageData.deletedAt,
      languageData.id
    );
  }

  async findAll(): Promise<Language[]> {
    const result = await db
      .select()
      .from(languages)
      .where(isNull(languages.deletedAt));
    
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<Language | null> {
    const result = await db
      .select()
      .from(languages)
      .where(
        and(
          eq(languages.id, id),
          isNull(languages.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findByCode(code: string): Promise<Language | null> {
    const result = await db
      .select()
      .from(languages)
      .where(
        and(
          eq(languages.code, code),
          isNull(languages.deletedAt)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(languageData: Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<Language> {
    const valuesToInsert = {
      name: languageData.name,
      code: languageData.code,
      nativeName: languageData.nativeName,
      flagUrl: languageData.flagUrl,
      isActive: languageData.isActive ?? true,
    };

    const result = await db
      .insert(languages)
      .values(valuesToInsert)
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, languageData: Partial<Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>): Promise<Language | null> {
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (languageData.name !== undefined) updateData.name = languageData.name;
    if (languageData.code !== undefined) updateData.code = languageData.code;
    if (languageData.nativeName !== undefined) updateData.nativeName = languageData.nativeName;
    if (languageData.flagUrl !== undefined) updateData.flagUrl = languageData.flagUrl;
    if (languageData.isActive !== undefined) updateData.isActive = languageData.isActive;

    const result = await db
      .update(languages)
      .set(updateData)
      .where(
        and(
          eq(languages.id, id),
          isNull(languages.deletedAt)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<boolean> {
    const result = await db
      .update(languages)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(languages.id, id),
          isNull(languages.deletedAt)
        )
      )
      .returning();
    
    return result.length > 0;
  }
}
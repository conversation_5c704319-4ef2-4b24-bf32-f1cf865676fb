import { Injectable } from '@nestjs/common';
import { IDailyGoalRepository } from '../../domain/ports/daily-goal.repository.port';
import { DailyGoal } from '../../domain/entities/daily-goal.entity';
import { db } from '../../../../database';

import { eq, and, gte, lte } from 'drizzle-orm';
import { dailyGoals } from '../../../../database/schemas/learning.schema';

@Injectable()
export class DailyGoalRepository implements IDailyGoalRepository {
  async findAll(): Promise<DailyGoal[]> {
    const result = await db.select().from(dailyGoals);
    return result.map(this.mapToDomain);
  }

  async findById(id: number): Promise<DailyGoal | null> {
    const result = await db
      .select()
      .from(dailyGoals)
      .where(eq(dailyGoals.id, id));
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async findByUserId(userId: number): Promise<DailyGoal[]> {
    const result = await db
      .select()
      .from(dailyGoals)
      .where(eq(dailyGoals.userId, userId))
      .orderBy(dailyGoals.date);
    
    return result.map(this.mapToDomain);
  }

  async findByUserAndDate(userId: number, date: Date): Promise<DailyGoal | null> {
    // Crear fechas para el inicio y fin del día
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    const result = await db
      .select()
      .from(dailyGoals)
      .where(
        and(
          eq(dailyGoals.userId, userId),
          gte(dailyGoals.date, startOfDay),
          lte(dailyGoals.date, endOfDay)
        )
      );
    
    if (result.length === 0) {
      return null;
    }
    
    return this.mapToDomain(result[0]);
  }

  async create(dailyGoal: Omit<DailyGoal, 'id' | 'createdAt' | 'updatedAt'>): Promise<DailyGoal> {
    const result = await db
      .insert(dailyGoals)
      .values(dailyGoal)
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async update(id: number, dailyGoal: Partial<DailyGoal>): Promise<DailyGoal> {
    const result = await db
      .update(dailyGoals)
      .set(dailyGoal)
      .where(eq(dailyGoals.id, id))
      .returning();
    
    return this.mapToDomain(result[0]);
  }

  async delete(id: number): Promise<void> {
    await db
      .delete(dailyGoals)
      .where(eq(dailyGoals.id, id));
  }

  private mapToDomain(raw: any): DailyGoal {
    return {
      id: raw.id,
      userId: raw.userId,
      date: raw.date,
      targetXp: raw.targetXp,
      achievedXp: raw.achievedXp,
      completed: raw.completed,
      completedAt: raw.completedAt,
      createdAt: raw.createdAt,
      updatedAt: raw.updatedAt
    };
  }
}
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetUserProgressBySubcategoryUseCase } from '../../application/usecases/user-progress/get-user-progress-by-subcategory.usecase';
import { AuthGuard } from '@core/auth/infrastructure/guards/auth.guard';
import { GetUser } from '@core/auth/infrastructure/decorators/get-user.decorator';

@ApiTags('learning/progress')
@Controller('learning/progress')
export class UserProgressController {
  constructor(
    private readonly getUserProgressBySubcategoryUseCase: GetUserProgressBySubcategoryUseCase,
  ) {}

  @Get('subcategory/:id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener progreso del usuario para una subcategoría' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Progreso del usuario recuperado con éxito' })
  @ApiResponse({ status: 404, description: 'Subcategoría no encontrada' })
  async getUserProgressForSubcategory(
    @GetUser('id') userId: number,
    @Param('id') subcategoryId: number
  ) {
    return this.getUserProgressBySubcategoryUseCase.execute(userId, subcategoryId);
  }
}
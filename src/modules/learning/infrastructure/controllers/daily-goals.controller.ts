import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@core/auth/infrastructure/guards/auth.guard';
import { GetUser } from '@core/auth/infrastructure/decorators/get-user.decorator';
import { GetCurrentDailyGoalUseCase } from '../../application/usecases/daily-goals/get-current-daily-goal.usecase';

@ApiTags('learning/daily-goals')
@Controller('learning/daily-goals')
export class DailyGoalsController {
  constructor(
    private readonly getCurrentDailyGoalUseCase: GetCurrentDailyGoalUseCase,
  ) {}

  @Get('current')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener el progreso del objetivo diario actual' })
  @ApiResponse({ status: 200, description: 'Objetivo diario obtenido con éxito' })
  async getCurrentDailyGoal(
    @GetUser('id') userId: number
  ) {
    return this.getCurrentDailyGoalUseCase.execute(userId);
  }
}
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { GetExercisesBySubcategoryUseCase } from '@modules/learning/application/usecases/exercise/get-exercises-by-subcategory.usecase';


@ApiTags('learning/exercises')
@Controller('learning/exercises')
export class ExerciseController {
  constructor(
    private readonly getExercisesBySubcategoryUseCase: GetExercisesBySubcategoryUseCase,
  ) {}

  @Get('subcategory/:id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener ejercicios por subcategoría' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Ejercicios obtenidos con éxito' })
  async getExercisesBySubcategory(
    @Param('id') subcategoryId: number
  ) {
    return this.getExercisesBySubcategoryUseCase.execute(subcategoryId);
  }
}
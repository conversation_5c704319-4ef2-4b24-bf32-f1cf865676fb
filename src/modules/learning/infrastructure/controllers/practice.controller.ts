import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { StartPracticeSessionUseCase } from '../../application/usecases/practice/start-practice-session.usecase';
import { EndPracticeSessionUseCase } from '../../application/usecases/practice/end-practice-session.usecase';
import { GetExercisesBySubcategoryUseCase } from '../../application/usecases/exercise/get-exercises-by-subcategory.usecase';
import { UpdateWordProgressUseCase } from '../../application/usecases/user-progress/update-word-progress.usecase';
import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { GetUser } from '@core/auth/infrastructure/decorators/get-user.decorator';


@ApiTags('learning/practice')
@Controller('learning/practice')
export class PracticeController {
    constructor(
        private readonly startPracticeSessionUseCase: StartPracticeSessionUseCase,
        private readonly endPracticeSessionUseCase: EndPracticeSessionUseCase,
        private readonly getExercisesBySubcategoryUseCase: GetExercisesBySubcategoryUseCase,
        private readonly updateWordProgressUseCase: UpdateWordProgressUseCase,
    ) { }


    @Post('sessions/start')
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Iniciar una sesión de práctica' })
    @ApiResponse({ status: 201, description: 'Sesión de práctica iniciada con éxito' })
    async startPracticeSession(
        @GetUser('id') userId: number,
        @Body() body: { subcategoryId?: number }
    ) {
        return this.startPracticeSessionUseCase.execute(userId, body.subcategoryId);
    }

    @Put('sessions/:id/end')
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Finalizar una sesión de práctica' })
    @ApiParam({ name: 'id', description: 'ID de la sesión de práctica' })
    @ApiResponse({ status: 200, description: 'Sesión de práctica finalizada con éxito' })
    async endPracticeSession(
        @GetUser('id') userId: number,
        @Param('id') sessionId: number,
        @Body() body: { xpEarned: number, correctAnswers: number , totalExercises: number }
    ) {
        return this.endPracticeSessionUseCase.execute(sessionId, userId, body);
    }

    @Get('subcategory/:id/exercises')
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Obtener ejercicios por subcategoría' })
    @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
    @ApiResponse({ status: 200, description: 'Ejercicios obtenidos con éxito' })
    async getExercisesBySubcategory(
        @Param('id') subcategoryId: number
    ) {
        return this.getExercisesBySubcategoryUseCase.execute(subcategoryId);
    }

    @Put('progress/word/:id')
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Actualizar progreso de una palabra' })
    @ApiParam({ name: 'id', description: 'ID de la palabra' })
    @ApiResponse({ status: 200, description: 'Progreso actualizado con éxito' })
    async updateWordProgress(
        @GetUser('id') userId: number,
        @Param('id') wordId: number,
        @Body() body: { isCorrect: boolean }
    ) {
        // Convertir el booleano isCorrect a un porcentaje (100% si es correcto, 0% si es incorrecto)
        const percentage = body.isCorrect ? 100 : 0;

        return this.updateWordProgressUseCase.execute(userId, wordId, percentage);
    }

}
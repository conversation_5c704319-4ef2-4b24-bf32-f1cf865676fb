import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';

import { GetUser } from '../../../../core/auth/infrastructure/decorators/get-user.decorator';
import { GetWordUseCase } from '@modules/learning/application/usecases/word/get-word.usecase';
import { GetWordsBySubcategoryUseCase } from '@modules/learning/application/usecases/word/get-words-by-subcategory.usecase';

@ApiTags('learning/words')
@Controller('learning/words')
export class WordController {
  constructor(
    private readonly getWordsBySubcategoryUseCase: GetWordsBySubcategoryUseCase,
    private readonly getWordUseCase: GetWordUseCase,
  ) {}

  @Get('subcategory/:id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener palabras por subcategoría' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Palabras obtenidas con éxito' })
  async getWordsBySubcategory(
    @Param('id') subcategoryId: number,
    @GetUser('id') userId: number
  ) {
    return this.getWordsBySubcategoryUseCase.execute(subcategoryId, userId);
  }

  @Get(':id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener detalles de una palabra' })
  @ApiParam({ name: 'id', description: 'ID de la palabra' })
  @ApiResponse({ status: 200, description: 'Palabra obtenida con éxito' })
  @ApiResponse({ status: 404, description: 'Palabra no encontrada' })
  async getWord(
    @Param('id') wordId: number,
    @GetUser('id') userId: number
  ) {
    return this.getWordUseCase.execute(wordId, userId);
  }
}
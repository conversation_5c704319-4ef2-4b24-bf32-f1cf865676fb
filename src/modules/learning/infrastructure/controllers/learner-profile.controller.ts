import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetLearnerProfileUseCase } from '../../application/usecases/learner-profile/get-learner-profile.usecase';

import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';

import { UpdateLearnerProfileDto } from '../../application/dtos/learner-profile/update-learner-profile.dto';
import { UpdateLearnerProfileUseCase } from '@modules/learning/application/usecases/learner-profile/update-learner-profile.usecase';
import { GetUser } from '@core/auth/infrastructure/decorators/get-user.decorator';

@ApiTags('learning/learner-profile')
@Controller('learning/learner-profile')
export class LearnerProfileController {
  constructor(
    private readonly getLearnerProfileUseCase: GetLearnerProfileUseCase,
    private readonly updateLearnerProfileUseCase: UpdateLearnerProfileUseCase,
  ) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener perfil de aprendiz del usuario actual' })
  @ApiResponse({ status: 200, description: 'Perfil de aprendiz obtenido con éxito' })
  async getLearnerProfile(
    @GetUser('id') userId: number
  ) {
    return this.getLearnerProfileUseCase.execute(userId);
  }

  @Put()
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Actualizar perfil de aprendiz del usuario actual' })
  @ApiResponse({ status: 200, description: 'Perfil de aprendiz actualizado con éxito' })
  async updateLearnerProfile(
    @GetUser('id') userId: number,
    @Body() updateLearnerProfileDto: UpdateLearnerProfileDto
  ) {
    return this.updateLearnerProfileUseCase.execute(userId, updateLearnerProfileDto);
  }
}
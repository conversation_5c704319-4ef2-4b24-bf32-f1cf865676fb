import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@core/auth/infrastructure/guards/auth.guard';
import { GetUser } from '@core/auth/infrastructure/decorators/get-user.decorator';
import { GetUnlockedAchievementsUseCase } from '../../application/usecases/achievements/get-unlocked-achievements.usecase';
import { GetRecentAchievementsUseCase } from '@modules/learning/application/usecases/achievements/get-recent-achievements.usecase';

@ApiTags('learning/achievements')
@Controller('learning/achievements')
export class AchievementsController {
  constructor(
    private readonly getUnlockedAchievementsUseCase: GetUnlockedAchievementsUseCase,
    private readonly getRecentAchievementsUseCase: GetRecentAchievementsUseCase,
  ) {}

  @Get('unlocked')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener logros desbloqueados recientemente' })
  @ApiResponse({ status: 200, description: 'Logros desbloqueados obtenidos con éxito' })
  async getUnlockedAchievements(
    @GetUser('id') userId: number
  ) {
    return this.getUnlockedAchievementsUseCase.execute(userId);
  }

  @Get('recent')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener logros recientes del sistema' })
  @ApiResponse({ status: 200, description: 'Logros recientes obtenidos con éxito' })
  async getRecentAchievements() {
    return this.getRecentAchievementsUseCase.execute();
  }
}
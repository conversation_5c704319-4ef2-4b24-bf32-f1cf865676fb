import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, <PERSON>piBearerAuth } from '@nestjs/swagger';
import { CreateSubcategoryDto } from '../../application/dtos/subcategory/create-subcategory.dto';
import { UpdateSubcategoryDto } from '../../application/dtos/subcategory/update-subcategory.dto';
import { GetSubcategoriesUseCase } from '../../application/usecases/subcategory/get-subcategories.usecase';
import { GetSubcategoryUseCase } from '../../application/usecases/subcategory/get-subcategory.usecase';
import { CreateSubcategoryUseCase } from '../../application/usecases/subcategory/create-subcategory.usecase';
import { UpdateSubcategoryUseCase } from '../../application/usecases/subcategory/update-subcategory.usecase';
import { DeleteSubcategoryUseCase } from '../../application/usecases/subcategory/delete-subcategory.usecase';
import { GetSubcategoriesByCategoryUseCase } from '../../application/usecases/subcategory/get-subcategories-by-category.usecase';
import { AuthGuard as JwtAuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { PermissionGuard } from '../../../../core/rbac/infrastructure/guards/permission.guard';
import { RequirePermissions } from '../../../../core/rbac/infrastructure/decorators/require-permissions.decorator';
import { GetSubcategoryDetailsUseCase } from '@modules/learning/application/usecases/subcategory/get-subcategory-details.usecase';

@ApiTags('learning/subcategories')
@Controller('learning/subcategories')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class SubcategoryController {
  constructor(
    private readonly getSubcategoriesUseCase: GetSubcategoriesUseCase,
    private readonly getSubcategoryUseCase: GetSubcategoryUseCase,
    private readonly createSubcategoryUseCase: CreateSubcategoryUseCase,
    private readonly updateSubcategoryUseCase: UpdateSubcategoryUseCase,
    private readonly deleteSubcategoryUseCase: DeleteSubcategoryUseCase,
    private readonly getSubcategoriesByCategoryUseCase: GetSubcategoriesByCategoryUseCase,
    private readonly getSubcategoryDetailsUseCase: GetSubcategoryDetailsUseCase,
  ) {}

  @Get()
  @RequirePermissions('learning:subcategories:read')
  @ApiOperation({ summary: 'Obtener todas las subcategorías' })
  @ApiQuery({ name: 'onlyActive', required: false, type: Boolean, description: 'Filtrar solo subcategorías activas' })
  @ApiResponse({ status: 200, description: 'Subcategorías recuperadas con éxito' })
  async getSubcategories(@Query('onlyActive') onlyActive?: string | boolean) {
    const filterActive = onlyActive === 'true' || onlyActive === true;
    return this.getSubcategoriesUseCase.execute(filterActive);
  }

  @Get(':id')
  @RequirePermissions('learning:subcategories:read')
  @ApiOperation({ summary: 'Obtener una subcategoría por ID' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Subcategoría recuperada con éxito' })
  @ApiResponse({ status: 404, description: 'Subcategoría no encontrada' })
  async getSubcategory(@Param('id') id: number) {
    return this.getSubcategoryUseCase.execute(+id);
  }

  @Get('by-category/:categoryId')
  @RequirePermissions('learning:subcategories:read')
  @ApiOperation({ summary: 'Obtener subcategorías por categoría' })
  @ApiParam({ name: 'categoryId', description: 'ID de la categoría' })
  @ApiResponse({ status: 200, description: 'Subcategorías recuperadas con éxito' })
  @ApiResponse({ status: 404, description: 'Categoría no encontrada' })
  async getSubcategoriesByCategory(@Param('categoryId') categoryId: number) {
    return this.getSubcategoriesByCategoryUseCase.execute(+categoryId);
  }

  @Post()
  @RequirePermissions('learning:subcategories:create')
  @ApiOperation({ summary: 'Crear una nueva subcategoría' })
  @ApiResponse({ status: 201, description: 'Subcategoría creada con éxito' })
  @ApiResponse({ status: 404, description: 'Categoría no encontrada' })
  async createSubcategory(@Body() createSubcategoryDto: CreateSubcategoryDto) {
    return this.createSubcategoryUseCase.execute(createSubcategoryDto);
  }

  @Put(':id')
  @RequirePermissions('learning:subcategories:update')
  @ApiOperation({ summary: 'Actualizar una subcategoría existente' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Subcategoría actualizada con éxito' })
  @ApiResponse({ status: 404, description: 'Subcategoría o categoría no encontrada' })
  async updateSubcategory(
    @Param('id') id: number,
    @Body() updateSubcategoryDto: UpdateSubcategoryDto,
  ) {
    return this.updateSubcategoryUseCase.execute(+id, updateSubcategoryDto);
  }

  @Delete(':id')
  @RequirePermissions('learning:subcategories:delete')
  @ApiOperation({ summary: 'Eliminar una subcategoría' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Subcategoría eliminada con éxito' })
  @ApiResponse({ status: 404, description: 'Subcategoría no encontrada' })
  async deleteSubcategory(@Param('id') id: number) {
    return this.deleteSubcategoryUseCase.execute(+id);
  }

  @Get(':id/details')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener detalles de una subcategoría incluyendo palabras y lecciones' })
  @ApiParam({ name: 'id', description: 'ID de la subcategoría' })
  @ApiResponse({ status: 200, description: 'Detalles de subcategoría recuperados con éxito' })
  @ApiResponse({ status: 404, description: 'Subcategoría no encontrada' })
  async getSubcategoryDetails(@Param('id') id: number) {
    return this.getSubcategoryDetailsUseCase.execute(+id);
  }
}
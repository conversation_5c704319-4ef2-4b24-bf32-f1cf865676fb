import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { CreateCategoryDto } from '../../application/dtos/category/create-category.dto';
import { UpdateCategoryDto } from '../../application/dtos/category/update-category.dto';
import { GetCategoriesUseCase } from '../../application/usecases/category/get-categories.usecase';
import { GetCategoryUseCase } from '../../application/usecases/category/get-category.usecase';
import { CreateCategoryUseCase } from '../../application/usecases/category/create-category.usecase';
import { UpdateCategoryUseCase } from '../../application/usecases/category/update-category.usecase';
import { DeleteCategoryUseCase } from '../../application/usecases/category/delete-category.usecase';
import { AuthGuard as JwtAuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { PermissionGuard } from '../../../../core/rbac/infrastructure/guards/permission.guard';
import { RequirePermissions } from '../../../../core/rbac/infrastructure/decorators/require-permissions.decorator';
import { GetCategoriesWithSubcategoriesUseCase } from '@modules/learning/application/usecases/category/get-categories-with-subcategories.usecase';

@ApiTags('learning/categories')
@Controller('learning/categories')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class CategoryController {
  constructor(
    private readonly getCategoriesUseCase: GetCategoriesUseCase,
    private readonly getCategoryUseCase: GetCategoryUseCase,
    private readonly createCategoryUseCase: CreateCategoryUseCase,
    private readonly updateCategoryUseCase: UpdateCategoryUseCase,
    private readonly deleteCategoryUseCase: DeleteCategoryUseCase,
    private readonly getCategoriesWithSubcategoriesUseCase: GetCategoriesWithSubcategoriesUseCase,
  ) {}

  @Get()
  @RequirePermissions('learning:categories:read')
  @ApiOperation({ summary: 'Obtener todas las categorías' })
  @ApiQuery({ name: 'onlyActive', required: false, type: Boolean, description: 'Filtrar solo categorías activas' })
  @ApiResponse({ status: 200, description: 'Categorías recuperadas con éxito' })
  async getCategories(@Query('onlyActive') onlyActive?: string | boolean) {
    const filterActive = onlyActive === 'true' || onlyActive === true;
    return this.getCategoriesUseCase.execute(filterActive);
  }

  @Get('with-subcategories')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener categorías con subcategorías y progreso' })
  @ApiQuery({ name: 'onlyActive', required: false, type: Boolean, description: 'Filtrar solo categorías activas' })
  @ApiResponse({ status: 200, description: 'Categorías con subcategorías recuperadas con éxito' })
  async getCategoriesWithSubcategories(
    @Req() req,
    @Query('onlyActive') onlyActive: boolean = true
  ) {
    const userId = req.user.id;
    return this.getCategoriesWithSubcategoriesUseCase.execute(userId, onlyActive);
  }

  @Get(':id')
  @RequirePermissions('learning:categories:read')
  @ApiOperation({ summary: 'Obtener una categoría por ID' })
  @ApiParam({ name: 'id', description: 'ID de la categoría' })
  @ApiResponse({ status: 200, description: 'Categoría recuperada con éxito' })
  @ApiResponse({ status: 404, description: 'Categoría no encontrada' })
  async getCategory(@Param('id') id: number) {
    return this.getCategoryUseCase.execute(+id);
  }

  @Post()
  @RequirePermissions('learning:categories:create')
  @ApiOperation({ summary: 'Crear una nueva categoría' })
  @ApiResponse({ status: 201, description: 'Categoría creada con éxito' })
  async createCategory(@Body() createCategoryDto: CreateCategoryDto) {
    return this.createCategoryUseCase.execute(createCategoryDto);
  }

  @Put(':id')
  @RequirePermissions('learning:categories:update')
  @ApiOperation({ summary: 'Actualizar una categoría existente' })
  @ApiParam({ name: 'id', description: 'ID de la categoría' })
  @ApiResponse({ status: 200, description: 'Categoría actualizada con éxito' })
  @ApiResponse({ status: 404, description: 'Categoría no encontrada' })
  async updateCategory(
    @Param('id') id: number,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ) {
    return this.updateCategoryUseCase.execute(+id, updateCategoryDto);
  }

  @Delete(':id')
  @RequirePermissions('learning:categories:delete')
  @ApiOperation({ summary: 'Eliminar una categoría' })
  @ApiParam({ name: 'id', description: 'ID de la categoría' })
  @ApiResponse({ status: 200, description: 'Categoría eliminada con éxito' })
  @ApiResponse({ status: 404, description: 'Categoría no encontrada' })
  async deleteCategory(@Param('id') id: number) {
    return this.deleteCategoryUseCase.execute(+id);
  }

}

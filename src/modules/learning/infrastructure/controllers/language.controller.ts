
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetLanguagesUseCase } from '../../application/usecases/language/get-languages.usecase';
import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';

@ApiTags('learning/languages')
@Controller('learning/languages')
export class LanguageController {
  constructor(
    private readonly getLanguagesUseCase: GetLanguagesUseCase,
  ) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener todos los idiomas' })
  @ApiResponse({ status: 200, description: 'Idiomas obtenidos con éxito' })
  async getLanguages() {
    return this.getLanguagesUseCase.execute();
  }
}
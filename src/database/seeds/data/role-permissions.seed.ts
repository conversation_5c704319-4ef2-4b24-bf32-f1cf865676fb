// Esta función toma los IDs de roles y permisos después de que se hayan insertado
// y crea las asignaciones correspondientes
export const createRolePermissionsSeed = (roleIds, permissionIds) => {
  // Asignar todos los permisos al rol de administrador
  const adminPermissions = Object.values(permissionIds).map(permissionId => ({
    roleId: roleIds.admin,
    permissionId,
    createdAt: new Date(),
  }));

  // Asignar permisos limitados al rol de usuario
  const userPermissions = [
    // Solo permisos de lectura para usuarios regulares
    { roleId: roleIds.user, permissionId: permissionIds.users_read, createdAt: new Date() },
    { roleId: roleIds.user, permissionId: permissionIds.roles_read, createdAt: new Date() },
    { roleId: roleIds.user, permissionId: permissionIds.permissions_read, createdAt: new Date() },
    { roleId: roleIds.user, permissionId: permissionIds.persons_read, createdAt: new Date() },
    // Permitir a los usuarios actualizar su propia información personal
    { roleId: roleIds.user, permissionId: permissionIds.persons_update, createdAt: new Date() },
  ];

  return [...adminPermissions, ...userPermissions];
};
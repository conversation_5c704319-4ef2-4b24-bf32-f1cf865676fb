// Esta función toma los IDs de usuarios y ejercicios después de que se hayan insertado
// y crea el historial de intentos correspondiente
export const createAttemptHistorySeed = (userIds, exerciseIds) => {
  return [
    // Intentos del administrador
    {
      userId: userIds.admin,
      exerciseId: exerciseIds.helloMultipleChoice,
      userResponse: 'Hello',
      isCorrect: true,
      timeTaken: 5,
      xpEarned: 10,
      attemptDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 días atrás
    },
    {
      userId: userIds.admin,
      exerciseId: exerciseIds.helloFillBlank,
      userResponse: 'Hello',
      isCorrect: true,
      timeTaken: 8,
      xpEarned: 15,
      attemptDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000), // 10 días atrás + 5 minutos
    },
    {
      userId: userIds.admin,
      exerciseId: exerciseIds.goodbyeMultipleChoice,
      userResponse: 'Goodbye',
      isCorrect: true,
      timeTaken: 4,
      xpEarned: 10,
      attemptDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 días atrás
    },
    
    // Intentos del usuario normal
    {
      userId: userIds.user,
      exerciseId: exerciseIds.helloMultipleChoice,
      userResponse: 'Hello',
      isCorrect: true,
      timeTaken: 12,
      xpEarned: 10,
      attemptDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 días atrás
    },
    {
      userId: userIds.user,
      exerciseId: exerciseIds.helloFillBlank,
      userResponse: 'Hi',
      isCorrect: false,
      timeTaken: 15,
      xpEarned: 0,
      attemptDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000), // 3 días atrás + 5 minutos
    },
    {
      userId: userIds.user,
      exerciseId: exerciseIds.helloFillBlank,
      userResponse: 'Hello',
      isCorrect: true,
      timeTaken: 10,
      xpEarned: 15,
      attemptDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000), // 3 días atrás + 10 minutos
    },
    {
      userId: userIds.user,
      exerciseId: exerciseIds.goodbyeMultipleChoice,
      userResponse: 'Hello',
      isCorrect: false,
      timeTaken: 8,
      xpEarned: 0,
      attemptDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 día atrás
    },
    {
      userId: userIds.user,
      exerciseId: exerciseIds.goodbyeMultipleChoice,
      userResponse: 'Goodbye',
      isCorrect: true,
      timeTaken: 6,
      xpEarned: 10,
      attemptDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000), // 1 día atrás + 5 minutos
    },
  ];
};
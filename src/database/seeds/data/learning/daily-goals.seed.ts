// Esta función toma los IDs de usuarios después de que se hayan insertado
// y crea los registros de metas diarias
export const createDailyGoalsSeed = (userIds) => {
    return [
      // Metas diarias del administrador
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 días atrás
        targetXp: 100,
        achievedXp: 100,
        completed: true,
        completedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 12 * 60 * 60 * 1000), // 10 días atrás + 12 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000), // 9 días atrás
        targetXp: 100,
        achievedXp: 120,
        completed: true,
        completedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000 + 10 * 60 * 60 * 1000), // 9 días atrás + 10 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 días atrás
        targetXp: 100,
        achievedXp: 150,
        completed: true,
        completedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000), // 8 días atrás + 8 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 días atrás
        targetXp: 100,
        achievedXp: 100,
        completed: true,
        completedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000), // 7 días atrás + 14 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 días atrás
        targetXp: 100,
        achievedXp: 110,
        completed: true,
        completedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000 + 9 * 60 * 60 * 1000), // 6 días atrás + 9 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 días atrás
        targetXp: 100,
        achievedXp: 130,
        completed: true,
        completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 11 * 60 * 60 * 1000), // 5 días atrás + 11 horas
      },
      {
        userId: userIds.admin,
        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 días atrás
        targetXp: 100,
        achievedXp: 100,
        completed: true,
        completedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000 + 13 * 60 * 60 * 1000), // 4 días atrás + 13 horas
      },
      
      // Metas diarias del usuario normal
      {
        userId: userIds.user,
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 días atrás
        targetXp: 50,
        achievedXp: 50,
        completed: true,
        completedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 18 * 60 * 60 * 1000), // 7 días atrás + 18 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 días atrás
        targetXp: 50,
        achievedXp: 60,
        completed: true,
        completedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000 + 16 * 60 * 60 * 1000), // 6 días atrás + 16 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 días atrás
        targetXp: 50,
        achievedXp: 55,
        completed: true,
        completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000), // 5 días atrás + 17 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 días atrás
        targetXp: 50,
        achievedXp: 50,
        completed: true,
        completedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000 + 19 * 60 * 60 * 1000), // 4 días atrás + 19 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 días atrás
        targetXp: 50,
        achievedXp: 50,
        completed: true,
        completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000), // 3 días atrás + 15 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 días atrás
        targetXp: 50,
        achievedXp: 50,
        completed: true,
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 20 * 60 * 60 * 1000), // 2 días atrás + 20 horas
      },
      {
        userId: userIds.user,
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 día atrás
        targetXp: 50,
        achievedXp: 50,
        completed: true,
        completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 18 * 60 * 60 * 1000), // 1 día atrás + 18 horas
      },
    ];
  };
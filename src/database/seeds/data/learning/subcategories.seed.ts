// Esta función toma los IDs de categorías después de que se hayan insertado
// y crea las subcategorías correspondientes
export const createSubcategoriesSeed = (categoryIds) => {
    return [
      // Subcategorías para Básicos
      {
        categoryId: categoryIds.basicos,
        name: '<PERSON><PERSON><PERSON>',
        description: 'Saludos y presentaciones básicas',
        iconUrl: '/assets/icons/greetings.png',
        order: 1,
        isActive: true,
        createdAt: new Date(),
      },
      {
        categoryId: categoryIds.basicos,
        name: 'Númer<PERSON>',
        description: 'Aprender a contar y usar números',
        iconUrl: '/assets/icons/numbers.png',
        order: 2,
        isActive: true,
        createdAt: new Date(),
      },
      {
        categoryId: categoryIds.basicos,
        name: 'Colores',
        description: 'Nombres de colores comunes',
        iconUrl: '/assets/icons/colors.png',
        order: 3,
        isActive: true,
        createdAt: new Date(),
      },
      
      // Subcategorías para Viajes
      {
        categoryId: categoryIds.viajes,
        name: 'Transporte',
        description: 'Vocabulario relacionado con diferentes medios de transporte',
        iconUrl: '/assets/icons/transport.png',
        order: 1,
        isActive: true,
        createdAt: new Date(),
      },
      {
        categoryId: categoryIds.viajes,
        name: 'Alojamiento',
        description: 'Términos útiles para hoteles y alojamientos',
        iconUrl: '/assets/icons/accommodation.png',
        order: 2,
        isActive: true,
        createdAt: new Date(),
      },
      
      // Subcategorías para Comida
      {
        categoryId: categoryIds.comida,
        name: 'Frutas y Verduras',
        description: 'Nombres de frutas y verduras comunes',
        iconUrl: '/assets/icons/fruits.png',
        order: 1,
        isActive: true,
        createdAt: new Date(),
      },
      {
        categoryId: categoryIds.comida,
        name: 'Restaurante',
        description: 'Frases útiles para pedir comida en restaurantes',
        iconUrl: '/assets/icons/restaurant.png',
        order: 2,
        isActive: true,
        createdAt: new Date(),
      },
      
      // Subcategorías para Familia
      {
        categoryId: categoryIds.familia,
        name: 'Miembros de la familia',
        description: 'Términos para diferentes miembros de la familia',
        iconUrl: '/assets/icons/family-members.png',
        order: 1,
        isActive: true,
        createdAt: new Date(),
      },
      
      // Subcategorías para Negocios
      {
        categoryId: categoryIds.negocios,
        name: 'Oficina',
        description: 'Vocabulario común en entornos de oficina',
        iconUrl: '/assets/icons/office.png',
        order: 1,
        isActive: true,
        createdAt: new Date(),
      },
      {
        categoryId: categoryIds.negocios,
        name: 'Reuniones',
        description: 'Frases útiles para reuniones de negocios',
        iconUrl: '/assets/icons/meetings.png',
        order: 2,
        isActive: true,
        createdAt: new Date(),
      },
    ];
  };
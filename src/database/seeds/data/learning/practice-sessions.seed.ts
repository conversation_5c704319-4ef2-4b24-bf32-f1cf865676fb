// Esta función toma los IDs de usuarios y subcategorías después de que se hayan insertado
// y crea las sesiones de práctica correspondientes
export const createPracticeSessionsSeed = (userIds, subcategoryIds) => {
  return [
    // Sesiones de práctica del administrador
    {
      userId: userIds.admin,
      subcategoryId: subcategoryIds.saludos,
      startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 días atrás
      endTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 15 * 60 * 1000), // 10 días atrás + 15 minutos
      totalXpEarned: 35,
      correctAnswers: 7,
      totalQuestions: 8,
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    },
    {
      userId: userIds.admin,
      subcategoryId: subcategoryIds.numeros,
      startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 días atrás
      endTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000), // 5 días atrás + 10 minutos
      totalXpEarned: 25,
      correctAnswers: 5,
      totalQuestions: 5,
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    },
    
    // Sesiones de práctica del usuario normal
    {
      userId: userIds.user,
      subcategoryId: subcategoryIds.saludos,
      startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 días atrás
      endTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 20 * 60 * 1000), // 3 días atrás + 20 minutos
      totalXpEarned: 25,
      correctAnswers: 5,
      totalQuestions: 8,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    },
    {
      userId: userIds.user,
      subcategoryId: subcategoryIds.colores,
      startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 día atrás
      endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 12 * 60 * 1000), // 1 día atrás + 12 minutos
      totalXpEarned: 15,
      correctAnswers: 3,
      totalQuestions: 5,
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
  ];
};
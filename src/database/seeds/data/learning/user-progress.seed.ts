// Esta función toma los IDs de usuarios y palabras después de que se hayan insertado
// y crea los registros de progreso correspondientes
export const createUserProgressSeed = (userIds, wordIds) => {
    return [
      // Progreso del administrador
      {
        userId: userIds.admin,
        wordId: wordIds.hello,
        learnedPercentage: 100,
        correctAttempts: 10,
        totalAttempts: 10,
        lastReviewDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 días atrás
        nextReviewDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 días en el futuro
        strengthLevel: 5,
        createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 días atrás
      },
      {
        userId: userIds.admin,
        wordId: wordIds.goodbye,
        learnedPercentage: 100,
        correctAttempts: 8,
        totalAttempts: 8,
        lastReviewDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 días atrás
        nextReviewDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 días en el futuro
        strengthLevel: 5,
        createdAt: new Date(Date.now() - 85 * 24 * 60 * 60 * 1000), // 85 días atrás
      },
      {
        userId: userIds.admin,
        wordId: wordIds.one,
        learnedPercentage: 100,
        correctAttempts: 7,
        totalAttempts: 7,
        lastReviewDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 días atrás
        nextReviewDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 días en el futuro
        strengthLevel: 5,
        createdAt: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000), // 80 días atrás
      },
      
      // Progreso del usuario normal
      {
        userId: userIds.user,
        wordId: wordIds.hello,
        learnedPercentage: 80,
        correctAttempts: 4,
        totalAttempts: 5,
        lastReviewDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 día atrás
        nextReviewDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 día en el futuro
        strengthLevel: 3,
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 días atrás
      },
      {
        userId: userIds.user,
        wordId: wordIds.goodbye,
        learnedPercentage: 60,
        correctAttempts: 3,
        totalAttempts: 5,
        lastReviewDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 día atrás
        nextReviewDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 día en el futuro
        strengthLevel: 2,
        createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 días atrás
      },
    ];
  };
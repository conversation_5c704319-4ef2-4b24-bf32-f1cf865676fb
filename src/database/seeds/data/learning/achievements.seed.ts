export const achievementsSeedData = [
    {
      name: 'Primer <PERSON>',
      description: 'Completa tu primera lección',
      iconUrl: '/assets/icons/achievements/first-step.png',
      requiredCondition: 'complete_first_lesson',
      xpReward: 50,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Racha de 7 días',
      description: 'Mantén una racha de aprendizaje durante 7 días consecutivos',
      iconUrl: '/assets/icons/achievements/streak-7.png',
      requiredCondition: 'streak_7_days',
      xpReward: 100,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Racha de 30 días',
      description: 'Mantén una racha de aprendizaje durante 30 días consecutivos',
      iconUrl: '/assets/icons/achievements/streak-30.png',
      requiredCondition: 'streak_30_days',
      xpReward: 300,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Vocabulario Básico',
      description: 'Aprende 50 palabras',
      iconUrl: '/assets/icons/achievements/vocab-50.png',
      requiredCondition: 'learn_50_words',
      xpReward: 150,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Maestro del Vocabulario',
      description: 'Aprende 500 palabras',
      iconUrl: '/assets/icons/achievements/vocab-500.png',
      requiredCondition: 'learn_500_words',
      xpReward: 1000,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Perfección',
      description: 'Completa una lección sin errores',
      iconUrl: '/assets/icons/achievements/perfect.png',
      requiredCondition: 'perfect_lesson',
      xpReward: 100,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Madrugador',
      description: 'Completa una lección antes de las 8 AM',
      iconUrl: '/assets/icons/achievements/early-bird.png',
      requiredCondition: 'early_morning_lesson',
      xpReward: 50,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Estudiante Nocturno',
      description: 'Completa una lección después de las 10 PM',
      iconUrl: '/assets/icons/achievements/night-owl.png',
      requiredCondition: 'late_night_lesson',
      xpReward: 50,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Políglota Principiante',
      description: 'Comienza a aprender un segundo idioma',
      iconUrl: '/assets/icons/achievements/polyglot.png',
      requiredCondition: 'start_second_language',
      xpReward: 200,
      isActive: true,
      createdAt: new Date(),
    },
    {
      name: 'Explorador de Categorías',
      description: 'Estudia palabras de todas las categorías disponibles',
      iconUrl: '/assets/icons/achievements/explorer.png',
      requiredCondition: 'study_all_categories',
      xpReward: 300,
      isActive: true,
      createdAt: new Date(),
    },
  ];
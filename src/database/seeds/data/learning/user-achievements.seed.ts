// Esta función toma los IDs de usuarios y logros después de que se hayan insertado
// y crea los logros de usuario correspondientes
export const createUserAchievementsSeed = (userIds, achievementIds) => {
    return [
      {
        userId: userIds.admin,
        achievementId: achievementIds.primerPaso,
        unlockedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 días atrás
      },
      {
        userId: userIds.admin,
        achievementId: achievementIds.racha7dias,
        unlockedAt: new Date(Date.now() - 85 * 24 * 60 * 60 * 1000), // 85 días atrás
      },
      {
        userId: userIds.admin,
        achievementId: achievementIds.racha30dias,
        unlockedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 días atrás
      },
      {
        userId: userIds.admin,
        achievementId: achievementIds.vocabularioBasico,
        unlockedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 días atrás
      },
      {
        userId: userIds.admin,
        achievementId: achievementIds.perfeccion,
        unlockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 días atrás
      },
      {
        userId: userIds.user,
        achievementId: achievementIds.primerPaso,
        unlockedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 días atrás
      },
      {
        userId: userIds.user,
        achievementId: achievementIds.racha7dias,
        unlockedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 días atrás
      },
    ];
  };
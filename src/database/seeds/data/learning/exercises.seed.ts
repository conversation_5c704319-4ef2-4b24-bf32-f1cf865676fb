// Esta función toma los IDs de palabras después de que se hayan insertado
// y crea los ejercicios correspondientes
export const createExercisesSeed = (wordIds) => {
  return [
    // Ejercicios para "Hello"
    {
      wordId: wordIds.hello,
      type: 'multiple_choice', // Esto será convertido al tipo enum en el seed principal
      question: '¿Cómo se dice "Hola" en inglés?',
      correctAnswer: 'Hello',
      options: JSON.stringify(['Hello', 'Goodbye', 'Thank you', 'Please']),
      hint: 'Es lo primero que dices cuando saludas a alguien.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.hello,
      type: 'fill_blank',
      question: 'Complete la frase: "_____, how are you?"',
      correctAnswer: 'Hello',
      hint: 'Es un saludo común.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Goodbye"
    {
      wordId: wordIds.goodbye,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Adiós" en inglés?',
      correctAnswer: 'Goodbye',
      options: JSON.stringify(['Hello', 'Goodbye', 'Thank you', 'Please']),
      hint: 'Es lo que dices cuando te despides.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    // Ejercicios para "Good afternoon"
    {
      wordId: wordIds.goodAfternoon,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Buenas tardes" en inglés?',
      correctAnswer: 'Good afternoon',
      options: JSON.stringify(['Good morning', 'Good afternoon', 'Good evening', 'Good night']),
      hint: 'Es lo que dices después del mediodía.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.goodAfternoon,
      type: 'fill_blank',
      question: 'Complete la frase: "_____ _____, welcome to our office."',
      correctAnswer: 'Good afternoon',
      hint: 'Es un saludo que se usa después del mediodía.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Good evening"
    {
      wordId: wordIds.goodEvening,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Buenas noches" (al llegar) en inglés?',
      correctAnswer: 'Good evening',
      options: JSON.stringify(['Good morning', 'Good afternoon', 'Good evening', 'Good night']),
      hint: 'Es lo que dices al llegar en la noche.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.goodEvening,
      type: 'listening',
      question: 'Escucha y selecciona la frase correcta.',
      correctAnswer: 'Good evening',
      options: JSON.stringify(['Good morning', 'Good afternoon', 'Good evening', 'Good night']),
      hint: 'Es un saludo que se usa al anochecer.',
      points: 20,
      timeLimit: 15,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Good night"
    {
      wordId: wordIds.goodNight,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Buenas noches" (al despedirse) en inglés?',
      correctAnswer: 'Good night',
      options: JSON.stringify(['Good morning', 'Good afternoon', 'Good evening', 'Good night']),
      hint: 'Es lo que dices al irte a dormir.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.goodNight,
      type: 'matching',
      question: 'Une la frase con su significado.',
      correctAnswer: 'Buenas noches (al despedirse)',
      options: JSON.stringify(['Buenos días', 'Buenas tardes', 'Buenas noches (al llegar)', 'Buenas noches (al despedirse)']),
      hint: 'Es lo que dices antes de irte a dormir.',
      points: 15,
      timeLimit: 25,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "How are you?"
    {
      wordId: wordIds.howAreYou,
      type: 'multiple_choice',
      question: '¿Cómo se dice "¿Cómo estás?" en inglés?',
      correctAnswer: 'How are you?',
      options: JSON.stringify(['How are you?', 'What is your name?', 'Where are you from?', 'How old are you?']),
      hint: 'Es una pregunta común después de saludar.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.howAreYou,
      type: 'speaking',
      question: 'Pronuncia la frase "How are you?"',
      correctAnswer: 'How are you?',
      hint: 'Es una pregunta sobre el estado de ánimo de alguien.',
      points: 25,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "I'm fine"
    {
      wordId: wordIds.imFine,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Estoy bien" en inglés?',
      correctAnswer: 'I\'m fine',
      options: JSON.stringify(['I\'m fine', 'I\'m tired', 'I\'m hungry', 'I\'m sad']),
      hint: 'Es una respuesta común a "How are you?"',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.imFine,
      type: 'fill_blank',
      question: 'Complete la frase: "How are you? _____ _____, thank you."',
      correctAnswer: 'I\'m fine',
      hint: 'Es una respuesta positiva sobre tu estado.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Thank you"
    {
      wordId: wordIds.thankYou,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Gracias" en inglés?',
      correctAnswer: 'Thank you',
      options: JSON.stringify(['Please', 'Sorry', 'Thank you', 'Excuse me']),
      hint: 'Es lo que dices cuando alguien te ayuda.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.thankYou,
      type: 'translation',
      question: 'Traduce "Gracias" al inglés.',
      correctAnswer: 'Thank you',
      hint: 'Es una expresión de gratitud.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "You're welcome"
    {
      wordId: wordIds.youreWelcome,
      type: 'multiple_choice',
      question: '¿Cómo se dice "De nada" en inglés?',
      correctAnswer: 'You\'re welcome',
      options: JSON.stringify(['Thank you', 'Please', 'You\'re welcome', 'Sorry']),
      hint: 'Es lo que respondes cuando alguien te da las gracias.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.youreWelcome,
      type: 'listening',
      question: 'Escucha y selecciona la frase correcta.',
      correctAnswer: 'You\'re welcome',
      options: JSON.stringify(['Thank you', 'Please', 'You\'re welcome', 'Sorry']),
      hint: 'Es una respuesta a un agradecimiento.',
      points: 20,
      timeLimit: 15,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Please"
    {
      wordId: wordIds.please,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Por favor" en inglés?',
      correctAnswer: 'Please',
      options: JSON.stringify(['Thank you', 'Please', 'Sorry', 'Excuse me']),
      hint: 'Es lo que dices cuando pides algo amablemente.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.please,
      type: 'fill_blank',
      question: 'Complete la frase: "_____, could you help me?"',
      correctAnswer: 'Please',
      hint: 'Es una palabra que hace que una petición sea más educada.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Excuse me"
    {
      wordId: wordIds.excuseMe,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Disculpe" en inglés?',
      correctAnswer: 'Excuse me',
      options: JSON.stringify(['Sorry', 'Please', 'Thank you', 'Excuse me']),
      hint: 'Es lo que dices para llamar la atención de alguien educadamente.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.excuseMe,
      type: 'speaking',
      question: 'Pronuncia la frase "Excuse me"',
      correctAnswer: 'Excuse me',
      hint: 'Es una forma educada de interrumpir o pedir permiso.',
      points: 25,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Sorry"
    {
      wordId: wordIds.sorry,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Lo siento" en inglés?',
      correctAnswer: 'Sorry',
      options: JSON.stringify(['Please', 'Thank you', 'Sorry', 'Excuse me']),
      hint: 'Es lo que dices cuando te disculpas por algo.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.sorry,
      type: 'translation',
      question: 'Traduce "Lo siento" al inglés.',
      correctAnswer: 'Sorry',
      hint: 'Es una expresión de disculpa.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Nice to meet you"
    {
      wordId: wordIds.niceToMeetYou,
      type: 'multiple_choice',
      question: '¿Cómo se dice "Encantado de conocerte" en inglés?',
      correctAnswer: 'Nice to meet you',
      options: JSON.stringify(['How are you?', 'Nice to meet you', 'See you later', 'Thank you']),
      hint: 'Es lo que dices cuando conoces a alguien por primera vez.',
      points: 10,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
    },
    {
      wordId: wordIds.niceToMeetYou,
      type: 'fill_blank',
      question: 'Complete la frase: "Hello, I\'m John. _____ _____ _____ _____!"',
      correctAnswer: 'Nice to meet you',
      hint: 'Es una expresión de cortesía al conocer a alguien.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "One"
    {
      wordId: wordIds.one,
      type: 'listening',
      question: 'Escucha y selecciona la palabra correcta.',
      correctAnswer: 'One',
      options: JSON.stringify(['One', 'Two', 'Three', 'Four']),
      hint: 'Es el primer número.',
      points: 20,
      timeLimit: 15,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Red"
    {
      wordId: wordIds.red,
      type: 'matching',
      question: 'Une la palabra con su significado.',
      correctAnswer: 'Rojo',
      options: JSON.stringify(['Rojo', 'Azul', 'Verde', 'Amarillo']),
      hint: 'Es el color de la sangre.',
      points: 15,
      timeLimit: 25,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Car"
    {
      wordId: wordIds.car,
      type: 'speaking',
      question: 'Pronuncia la palabra "Car".',
      correctAnswer: 'Car',
      hint: 'Es un vehículo con cuatro ruedas.',
      points: 25,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
    
    // Ejercicios para "Apple"
    {
      wordId: wordIds.apple,
      type: 'translation',
      question: 'Traduce "Manzana" al inglés.',
      correctAnswer: 'Apple',
      hint: 'Es una fruta roja o verde.',
      points: 15,
      timeLimit: 20,
      isActive: true,
      createdAt: new Date(),
    },
  ];
};
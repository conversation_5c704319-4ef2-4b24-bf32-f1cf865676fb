export const permissionsSeedData = [
  // Permisos para usuarios
  {
    name: 'users_read',
    description: 'View users',
    resource: 'users',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'users_create',
    description: 'Create users',
    resource: 'users',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'users_update',
    description: 'Update users',
    resource: 'users',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'users_delete',
    description: 'Delete users',
    resource: 'users',
    action: 'delete',
    createdAt: new Date(),
  },
  
  // Permisos para roles
  {
    name: 'roles_read',
    description: 'View roles',
    resource: 'roles',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'roles_create',
    description: 'Create roles',
    resource: 'roles',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'roles_update',
    description: 'Update roles',
    resource: 'roles',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'roles_delete',
    description: 'Delete roles',
    resource: 'roles',
    action: 'delete',
    createdAt: new Date(),
  },
  {
    name: 'roles_assign',
    description: 'Assign roles to users',
    resource: 'roles',
    action: 'assign',
    createdAt: new Date(),
  },
  
  // Permisos para permisos
  {
    name: 'permissions_read',
    description: 'View permissions',
    resource: 'permissions',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'permissions_create',
    description: 'Create permissions',
    resource: 'permissions',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'permissions_update',
    description: 'Update permissions',
    resource: 'permissions',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'permissions_delete',
    description: 'Delete permissions',
    resource: 'permissions',
    action: 'delete',
    createdAt: new Date(),
  },
  {
    name: 'permissions_assign',
    description: 'Assign permissions to roles',
    resource: 'permissions',
    action: 'assign',
    createdAt: new Date(),
  },
  
  // Permisos para personas
  {
    name: 'persons_read',
    description: 'View persons',
    resource: 'persons',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'persons_create',
    description: 'Create persons',
    resource: 'persons',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'persons_update',
    description: 'Update persons',
    resource: 'persons',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'persons_delete',
    description: 'Delete persons',
    resource: 'persons',
    action: 'delete',
    createdAt: new Date(),
  },
  {
    name: 'learning:categories:read',
    description: 'View categories',
    resource: 'learning:categories',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'learning:categories:create',
    description: 'Create categories',
    resource: 'learning:categories',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'learning:categories:update',
    description: 'Update categories',
    resource: 'learning:categories',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'learning:categories:delete',
    description: 'Delete categories',
    resource: 'learning:categories',
    action: 'delete',
    createdAt: new Date(),
  },
  {
    name: 'learning:subcategories:read',
    description: 'View subcategories',
    resource: 'learning:subcategories',
    action: 'read',
    createdAt: new Date(),
  },
  {
    name: 'learning:subcategories:create',
    description: 'Create subcategories',
    resource: 'learning:subcategories',
    action: 'create',
    createdAt: new Date(),
  },
  {
    name: 'learning:subcategories:update',
    description: 'Update subcategories',
    resource: 'learning:subcategories',
    action: 'update',
    createdAt: new Date(),
  },
  {
    name: 'learning:subcategories:delete',
    description: 'Delete subcategories',
    resource: 'learning:subcategories',
    action: 'delete',
    createdAt: new Date(),
  },
];
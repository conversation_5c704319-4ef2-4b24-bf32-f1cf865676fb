// Esta función toma los IDs de usuarios después de que se hayan insertado
// y crea las personas correspondientes
export const createPersonsSeed = (userIds) => {
    return [
      {
        userId: userIds.admin,
        firstName: 'Admin',
        lastName: 'User',
        middleName: '',
        email: '<EMAIL>',
        alternativeEmail: '<EMAIL>',
        phoneNumber: '+1234567890',
        mobileNumber: '+1234567891',
        address: '123 Admin St, Admin City',
        gender: 'other',
        birthDate: new Date('1990-01-01'),
        documentNumber: 'ADM123456',
        documentType: 'id',
        createdAt: new Date(),
      },
      {
        userId: userIds.user,
        firstName: 'Regular',
        lastName: 'User',
        middleName: '',
        email: '<EMAIL>',
        alternativeEmail: '<EMAIL>',
        phoneNumber: '+1234567892',
        mobileNumber: '+1234567893',
        address: '456 User St, User City',
        gender: 'other',
        birthDate: new Date('1995-05-05'),
        documentNumber: 'USR654321',
        documentType: 'id',
        createdAt: new Date(),
      },
    ];
  };
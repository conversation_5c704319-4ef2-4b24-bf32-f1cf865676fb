import { db } from '../index';
import {
  languages,
  categories,
  subcategories,
  levels,
  words,
  exercises,
  learnerProfiles,
  userProgress,
  achievements,
  userAchievements,
  userSettings,
  practiceSessions, // Corregido: practiceSession -> practiceSessions
  attemptHistory,
  dailyGoals
} from '../schemas/learning.schema';
import { users } from '../schemas/auth.schema';

// Importar datos de seed
import { languagesSeedData } from './data/learning/languages.seed';
import { categoriesSeedData } from './data/learning/categories.seed';
import { createSubcategoriesSeed } from './data/learning/subcategories.seed';
import { levelsSeedData } from './data/learning/levels.seed';
import { createWordsSeed } from './data/learning/words.seed';
import { createExercisesSeed } from './data/learning/exercises.seed';
import { createLearnerProfilesSeed } from './data/learning/learner-profiles.seed';
import { createUserProgressSeed } from './data/learning/user-progress.seed';
import { achievementsSeedData } from './data/learning/achievements.seed';
import { createUserAchievementsSeed } from './data/learning/user-achievements.seed';
import { createUserSettingsSeed } from './data/learning/user-settings.seed';
import { createPracticeSessionsSeed } from './data/learning/practice-sessions.seed';
import { createAttemptHistorySeed } from './data/learning/attempt-history.seed'; // Corregir nombre del archivo
import { createDailyGoalsSeed } from './data/learning/daily-goals.seed';

// Función para limpiar las tablas de Learning
async function cleanLearningTables() {
  console.log('🧹 Limpiando tablas de Learning...');

  // Eliminar datos en orden inverso para evitar problemas de restricciones de clave foránea
  // await db.delete(dailyGoals); // Comentamos esta línea ya que dailyGoals no está definido
  await db.delete(attemptHistory);
  await db.delete(userAchievements);
  await db.delete(userProgress);
  await db.delete(learnerProfiles);
  await db.delete(exercises);
  await db.delete(words);
  await db.delete(subcategories);
  await db.delete(categories);
  await db.delete(achievements);
  await db.delete(levels);
  await db.delete(languages);

  console.log('✅ Tablas de Learning limpiadas correctamente');
}

// Función principal para sembrar datos de Learning
export async function seedLearning() {
  try {
    await cleanLearningTables();
    console.log('🌱 Sembrando datos de Learning...');

    // Obtener IDs de usuarios existentes
    const usersResult = await db.select({ id: users.id, username: users.username }).from(users);
    const userIds = {
      admin: usersResult.find(u => u.username === 'admin')?.id,
      user: usersResult.find(u => u.username === 'user')?.id,
    };

    if (!userIds.admin || !userIds.user) {
      throw new Error('No se encontraron los usuarios necesarios. Ejecute primero el seed principal.');
    }

    // Insertar idiomas
    const languagesResult = await db.insert(languages).values(languagesSeedData).returning({ id: languages.id, code: languages.code });
    console.log('✅ Idiomas sembrados correctamente');

    // Mapear IDs de idiomas
    const languageIds = {
      es: languagesResult.find(l => l.code === 'es')?.id,
      en: languagesResult.find(l => l.code === 'en')?.id,
      fr: languagesResult.find(l => l.code === 'fr')?.id,
      de: languagesResult.find(l => l.code === 'de')?.id,
      it: languagesResult.find(l => l.code === 'it')?.id,
    };

    // Insertar categorías
    const categoriesResult = await db.insert(categories).values(categoriesSeedData).returning({ id: categories.id, name: categories.name });
    console.log('✅ Categorías sembradas correctamente');

    // Mapear IDs de categorías
    const categoryIds = {
      basicos: categoriesResult.find(c => c.name === 'Básicos')?.id,
      viajes: categoriesResult.find(c => c.name === 'Viajes')?.id,
      comida: categoriesResult.find(c => c.name === 'Comida')?.id,
      familia: categoriesResult.find(c => c.name === 'Familia')?.id,
      negocios: categoriesResult.find(c => c.name === 'Negocios')?.id,
    };

    // Insertar subcategorías
    const subcategoriesSeed = createSubcategoriesSeed(categoryIds);
    const subcategoriesResult = await db.insert(subcategories).values(subcategoriesSeed).returning({ id: subcategories.id, name: subcategories.name });
    console.log('✅ Subcategorías sembradas correctamente');

    // Mapear IDs de subcategorías
    const subcategoryIds = {
      saludos: subcategoriesResult.find(s => s.name === 'Saludos')?.id,
      numeros: subcategoriesResult.find(s => s.name === 'Números')?.id,
      colores: subcategoriesResult.find(s => s.name === 'Colores')?.id,
      transporte: subcategoriesResult.find(s => s.name === 'Transporte')?.id,
      alojamiento: subcategoriesResult.find(s => s.name === 'Alojamiento')?.id,
      frutasVerduras: subcategoriesResult.find(s => s.name === 'Frutas y Verduras')?.id,
      restaurante: subcategoriesResult.find(s => s.name === 'Restaurante')?.id,
      miembrosFamilia: subcategoriesResult.find(s => s.name === 'Miembros de la familia')?.id,
      oficina: subcategoriesResult.find(s => s.name === 'Oficina')?.id,
      reuniones: subcategoriesResult.find(s => s.name === 'Reuniones')?.id,
    };

    // Insertar niveles
    const levelsResult = await db.insert(levels).values(levelsSeedData).returning({ id: levels.id, name: levels.name });
    console.log('✅ Niveles sembrados correctamente');

    // Insertar palabras
    const wordsSeed = createWordsSeed(subcategoryIds, languageIds);
    const wordsResult = await db.insert(words).values(wordsSeed).returning({ id: words.id, originalWord: words.originalWord });
    console.log('✅ Palabras sembradas correctamente');

    // Mapear IDs de palabras
    const wordIds = {
      hello: wordsResult.find(w => w.originalWord === 'Hello')?.id,
      goodbye: wordsResult.find(w => w.originalWord === 'Goodbye')?.id,
      goodMorning: wordsResult.find(w => w.originalWord === 'Good morning')?.id,
      one: wordsResult.find(w => w.originalWord === 'One')?.id,
      two: wordsResult.find(w => w.originalWord === 'Two')?.id,
      red: wordsResult.find(w => w.originalWord === 'Red')?.id,
      blue: wordsResult.find(w => w.originalWord === 'Blue')?.id,
      car: wordsResult.find(w => w.originalWord === 'Car')?.id,
      train: wordsResult.find(w => w.originalWord === 'Train')?.id,
      apple: wordsResult.find(w => w.originalWord === 'Apple')?.id,
      tomato: wordsResult.find(w => w.originalWord === 'Tomato')?.id,
      goodAfternoon: wordsResult.find(w => w.originalWord === 'Good afternoon')?.id,
      goodEvening: wordsResult.find(w => w.originalWord === 'Good evening')?.id,
      goodNight: wordsResult.find(w => w.originalWord === 'Good night')?.id,
      howAreYou: wordsResult.find(w => w.originalWord === 'How are you?')?.id,
      imFine: wordsResult.find(w => w.originalWord === 'I\'m fine')?.id,
      thankYou: wordsResult.find(w => w.originalWord === 'Thank you')?.id,
      youreWelcome: wordsResult.find(w => w.originalWord === 'You\'re welcome')?.id,
      please: wordsResult.find(w => w.originalWord === 'Please')?.id,
      excuseMe: wordsResult.find(w => w.originalWord === 'Excuse me')?.id,
      sorry: wordsResult.find(w => w.originalWord === 'Sorry')?.id,
      niceToMeetYou: wordsResult.find(w => w.originalWord === 'Nice to meet you')?.id,
      seeYouLater: wordsResult.find(w => w.originalWord === 'See you later')?.id,
      seeYouTomorrow: wordsResult.find(w => w.originalWord === 'See you tomorrow')?.id,
      haveANiceDay: wordsResult.find(w => w.originalWord === 'Have a nice day')?.id,
      welcome: wordsResult.find(w => w.originalWord === 'Welcome')?.id,
      howsItGoing: wordsResult.find(w => w.originalWord === 'How\'s it going?')?.id,
      whatsUp: wordsResult.find(w => w.originalWord === 'What\'s up?')?.id,
      takeCare: wordsResult.find(w => w.originalWord === 'Take care')?.id,
      goodLuck: wordsResult.find(w => w.originalWord === 'Good luck')?.id,
      congratulations: wordsResult.find(w => w.originalWord === 'Congratulations')?.id,
    };

    // Insertar ejercicios - Corregimos el tipado
    const exercisesSeed = createExercisesSeed(wordIds);
    // Convertimos el tipo string a un tipo válido del enum
    const typedExercisesSeed = exercisesSeed.map(exercise => ({
      ...exercise,
      type: exercise.type as any // Usamos any para evitar problemas de tipado
    }));

    const exercisesResult = await db.insert(exercises).values(typedExercisesSeed).returning({ id: exercises.id, wordId: exercises.wordId, type: exercises.type });
    console.log('✅ Ejercicios sembrados correctamente');

    // Mapear IDs de ejercicios
    const exerciseIds = {
      helloMultipleChoice: exercisesResult.find(e => e.wordId === wordIds.hello && e.type === 'multiple_choice')?.id,
      helloFillBlank: exercisesResult.find(e => e.wordId === wordIds.hello && e.type === 'fill_blank')?.id,
      goodbyeMultipleChoice: exercisesResult.find(e => e.wordId === wordIds.goodbye && e.type === 'multiple_choice')?.id,
      oneListening: exercisesResult.find(e => e.wordId === wordIds.one && e.type === 'listening')?.id,
      redMatching: exercisesResult.find(e => e.wordId === wordIds.red && e.type === 'matching')?.id,
      carSpeaking: exercisesResult.find(e => e.wordId === wordIds.car && e.type === 'speaking')?.id,
      appleTranslation: exercisesResult.find(e => e.wordId === wordIds.apple && e.type === 'translation')?.id,
    };

    // Insertar logros
    const achievementsResult = await db.insert(achievements).values(achievementsSeedData).returning({ id: achievements.id, name: achievements.name });
    console.log('✅ Logros sembrados correctamente');

    // Mapear IDs de logros
    const achievementIds = {
      primerPaso: achievementsResult.find(a => a.name === 'Primer Paso')?.id,
      racha7dias: achievementsResult.find(a => a.name === 'Racha de 7 días')?.id,
      racha30dias: achievementsResult.find(a => a.name === 'Racha de 30 días')?.id,
      vocabularioBasico: achievementsResult.find(a => a.name === 'Vocabulario Básico')?.id,
      maestroVocabulario: achievementsResult.find(a => a.name === 'Maestro del Vocabulario')?.id,
      perfeccion: achievementsResult.find(a => a.name === 'Perfección')?.id,
      madrugador: achievementsResult.find(a => a.name === 'Madrugador')?.id,
      estudianteNocturno: achievementsResult.find(a => a.name === 'Estudiante Nocturno')?.id,
    };

    // Insertar perfiles de aprendizaje
    const learnerProfilesSeed = createLearnerProfilesSeed(userIds, languageIds);
    await db.insert(learnerProfiles).values(learnerProfilesSeed);
    console.log('✅ Perfiles de aprendizaje sembrados correctamente');

    // Insertar progreso de usuario
    const userProgressSeed = createUserProgressSeed(userIds, wordIds);
    await db.insert(userProgress).values(userProgressSeed);
    console.log('✅ Progreso de usuario sembrado correctamente');

    // Insertar logros de usuario
    const userAchievementsSeed = createUserAchievementsSeed(userIds, achievementIds);
    await db.insert(userAchievements).values(userAchievementsSeed);
    console.log('✅ Logros de usuario sembrados correctamente');

    // Insertar configuraciones de usuario
    const userSettingsSeed = createUserSettingsSeed(userIds);
    await db.insert(userSettings).values(userSettingsSeed);
    console.log('✅ Configuraciones de usuario sembradas correctamente');

    // Insertar sesiones de práctica
    const practiceSessionsSeed = createPracticeSessionsSeed(userIds, subcategoryIds);
    await db.insert(practiceSessions).values(practiceSessionsSeed); // Corregido: practiceSession -> practiceSessions
    console.log('✅ Sesiones de práctica sembradas correctamente');

    // Insertar historial de intentos
    const attemptHistorySeed = createAttemptHistorySeed(userIds, exerciseIds);
    await db.insert(attemptHistory).values(attemptHistorySeed);
    console.log('✅ Historial de intentos sembrado correctamente');

    // Comentamos la inserción de metas diarias ya que no está definido en el esquema
    const dailyGoalsSeed = createDailyGoalsSeed(userIds);
    await db.insert(dailyGoals).values(dailyGoalsSeed);
    console.log('✅ Metas diarias sembradas correctamente');

    console.log('✅ Datos de Learning sembrados correctamente');
    return true;
  } catch (error) {
    console.error('❌ Error al sembrar datos de Learning:', error);
    throw error;
  }
}

// Función para ejecutar el seed directamente si se llama al archivo
if (require.main === module) {
  seedLearning()
    .then(() => {
      console.log('✅ Proceso de siembra de Learning completado con éxito');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en el proceso de siembra de Learning:', error);
      process.exit(1);
    });
}
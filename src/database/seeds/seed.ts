import { db } from '../index';
import { users, roles, permissions, userRoles, rolePermissions, tokens, persons } from '../schemas/auth.schema';
import { usersSeedData } from './data/users.seed';
import { rolesSeedData } from './data/roles.seed';
import { permissionsSeedData } from './data/permissions.seed';
import { createUserRolesSeed } from './data/user-roles.seed';
import { createRolePermissionsSeed } from './data/role-permissions.seed';
import { createPersonsSeed } from './data/persons.seed';

async function cleanDatabase() {
  console.log('🧹 Cleaning database...');
  // Eliminar en orden para respetar las restricciones de clave foránea
  await db.delete(tokens);
  await db.delete(rolePermissions);
  await db.delete(userRoles);
  await db.delete(persons);
  await db.delete(permissions);
  await db.delete(roles);
  await db.delete(users);
}

async function seed() {
  try {
    await cleanDatabase();
    console.log('🌱 Seeding database...');
    
    // Insertar usuarios
    const usersResult = await db.insert(users).values(usersSeedData).returning({ id: users.id, username: users.username });
    console.log('✅ Users seeded successfully');
    
    // Mapear IDs de usuarios para referencia fácil
    const userIds = {
      admin: usersResult.find(u => u.username === 'admin')?.id,
      user: usersResult.find(u => u.username === 'user')?.id,
    };
    
    // Insertar información personal de usuarios
    const personsSeed = createPersonsSeed(userIds);
    await db.insert(persons).values(personsSeed);
    console.log('✅ Persons seeded successfully');
    
    // Insertar roles
    const rolesResult = await db.insert(roles).values(rolesSeedData).returning({ id: roles.id, name: roles.name });
    console.log('✅ Roles seeded successfully');
    
    // Mapear IDs de roles para referencia fácil
    const roleIds = {
      admin: rolesResult.find(r => r.name === 'admin')?.id,
      user: rolesResult.find(r => r.name === 'user')?.id,
    };
    
    // Insertar permisos
    const permissionsResult = await db.insert(permissions).values(permissionsSeedData).returning({ id: permissions.id, name: permissions.name });
    console.log('✅ Permissions seeded successfully');
    
    // Mapear IDs de permisos para referencia fácil
    const permissionIds = {};
    permissionsResult.forEach(p => {
      permissionIds[p.name] = p.id;
    });
    
    // Asignar roles a usuarios
    const userRolesSeed = createUserRolesSeed(userIds, roleIds);
    await db.insert(userRoles).values(userRolesSeed);
    console.log('✅ User roles assigned successfully');
    
    // Asignar permisos a roles
    const rolePermissionsSeed = createRolePermissionsSeed(roleIds, permissionIds);
    await db.insert(rolePermissions).values(rolePermissionsSeed);
    console.log('✅ Role permissions assigned successfully');
    
    console.log('✅ Seed completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

seed();
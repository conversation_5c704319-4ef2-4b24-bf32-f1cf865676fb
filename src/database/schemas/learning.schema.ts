// Importación de tipos y funciones de Drizzle ORM para definir el esquema de base de datos en PostgreSQL
import { pgTable, serial, varchar, text, integer, timestamp, boolean, real, smallint, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { users } from './auth.schema';

// Definición del esquema personalizado 'learning' para agrupar las tablas relacionadas
export const learning = pgSchema('learning');

// Campos comunes de timestamps reutilizables en todas las tablas
const timestamps = {
  createdAt: timestamp('created_at').defaultNow().notNull(), // Fecha de creación
  updatedAt: timestamp('updated_at').$onUpdate(() => new Date()), // Fecha de última actualización
  deletedAt: timestamp('deleted_at'), // Fecha de eliminación lógica
};

// Enumeración para representar niveles de dificultad
export const difficultyLevelEnum = learning.enum('difficulty_level', ['beginner', 'elementary', 'intermediate', 'advanced', 'proficient']);


// Enumeración para tipos de ejercicios
export const exerciseTypeEnum = learning.enum('exercise_type', ['multiple_choice', 'fill_blank', 'listening', 'speaking', 'matching', 'translation']);

// Tabla de idiomas disponibles en la plataforma
export const languages = learning.table('languages', {
  id: serial('id').primaryKey(),
  code: varchar('code', { length: 10 }).notNull().unique(), // Código del idioma (ISO)
  name: varchar('name', { length: 100 }).notNull(), // Nombre del idioma
  nativeName: varchar('native_name', { length: 100 }).notNull(), // Nombre nativo del idioma
  flagUrl: varchar('flag_url', { length: 255 }), // URL de la bandera representativa
  isActive: boolean('is_active').default(true).notNull(), // Estado de actividad
  ...timestamps,
});

// Tabla de categorías generales de contenido
export const categories = learning.table('categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(), // Nombre de la categoría
  description: text('description'), // Descripción opcional
  iconUrl: varchar('icon_url', { length: 255 }), // Icono representativo
  order: integer('order').default(0), // Orden de aparición
  isActive: boolean('is_active').default(true).notNull(),
  ...timestamps,
});

// Tabla de subcategorías, relacionadas con una categoría principal
export const subcategories = learning.table('subcategories', {
  id: serial('id').primaryKey(),
  categoryId: integer('category_id').notNull().references(() => categories.id), // FK a la categoría
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  iconUrl: varchar('icon_url', { length: 255 }),
  order: integer('order').default(0),
  isActive: boolean('is_active').default(true).notNull(),
  ...timestamps,
});

// Tabla de niveles de usuario, basado en experiencia
export const levels = learning.table('levels', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(), // Nombre del nivel
  description: text('description'), // Descripción del nivel
  requiredXp: integer('required_xp').default(0).notNull(), // Experiencia necesaria para alcanzar el nivel
  iconUrl: varchar('icon_url', { length: 255 }), // Icono representativo
  order: integer('order').default(0), // Orden de presentación
  ...timestamps,
});

// Tabla que almacena palabras o frases para el aprendizaje
export const words = learning.table('words', {
  id: serial('id').primaryKey(),
  subcategoryId: integer('subcategory_id').notNull().references(() => subcategories.id), // FK a subcategoría
  languageId: integer('language_id').notNull().references(() => languages.id), // FK a idioma
  originalWord: varchar('original_word', { length: 255 }).notNull(), // Palabra original
  meaning: text('meaning').notNull(), // Significado
  example: text('example'), // Ejemplo de uso
  audioUrl: varchar('audio_url', { length: 255 }), // URL del audio
  imageUrl: varchar('image_url', { length: 255 }), // Imagen ilustrativa
  difficultyLevel: smallint('difficulty_level').default(1).notNull(), // Nivel de dificultad
  isActive: boolean('is_active').default(true).notNull(),
  ...timestamps,
});

// Tabla de ejercicios asociados a palabras
export const exercises = learning.table('exercises', {
  id: serial('id').primaryKey(),
  wordId: integer('word_id').notNull().references(() => words.id), // FK a palabra
  type: exerciseTypeEnum('type').notNull(), // Tipo de ejercicio
  question: text('question').notNull(), // Pregunta
  correctAnswer: text('correct_answer').notNull(), // Respuesta correcta
  options: text('options'), // Opciones (si aplica)
  hint: text('hint'), // Pista opcional
  points: integer('points').default(10).notNull(), // Puntos otorgados
  timeLimit: integer('time_limit'), // Límite de tiempo
  isActive: boolean('is_active').default(true).notNull(),
  ...timestamps,
});

// Tabla de perfiles de los aprendices
export const learnerProfiles = learning.table('learner_profiles', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id).unique(), // Usuario relacionado
  firstName: varchar('first_name', { length: 100 }), // Nombre
  lastName: varchar('last_name', { length: 100 }), // Apellido
  profilePicture: varchar('profile_picture', { length: 255 }), // Imagen de perfil
  currentLevel: integer('current_level').default(1).notNull(), // Nivel actual
  totalXp: integer('total_xp').default(0).notNull(), // XP total
  streak: integer('streak').default(0).notNull(), // Racha de días activos
  lastActiveDate: timestamp('last_active_date').defaultNow().notNull(), // Última actividad
  preferredLanguageId: integer('preferred_language_id').references(() => languages.id), // Idioma preferido
  ...timestamps,
});

// Tabla de progreso de aprendizaje por palabra
export const userProgress = learning.table('user_progress', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  wordId: integer('word_id').notNull().references(() => words.id),
  learnedPercentage: real('learned_percentage').default(0).notNull(), // Porcentaje de aprendizaje
  correctAttempts: integer('correct_attempts').default(0).notNull(), // Intentos correctos
  totalAttempts: integer('total_attempts').default(0).notNull(), // Intentos totales
  lastReviewDate: timestamp('last_review_date').defaultNow().notNull(), // Última revisión
  nextReviewDate: timestamp('next_review_date'), // Próxima revisión programada
  strengthLevel: smallint('strength_level').default(0).notNull(), // Nivel de dominio
  ...timestamps,
});

// Tabla de logros disponibles
export const achievements = learning.table('achievements', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(), // Nombre del logro
  description: text('description').notNull(), // Descripción del logro
  iconUrl: varchar('icon_url', { length: 255 }), // Icono
  requiredCondition: text('required_condition').notNull(), // Condición para desbloqueo
  xpReward: integer('xp_reward').default(0).notNull(), // XP otorgado
  isActive: boolean('is_active').default(true).notNull(),
  ...timestamps,
});

// Tabla de logros obtenidos por usuario
export const userAchievements = learning.table('user_achievements', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  achievementId: integer('achievement_id').notNull().references(() => achievements.id),
  unlockedAt: timestamp('unlocked_at').defaultNow().notNull(), // Fecha de desbloqueo
  ...timestamps,
});

// Tabla de configuración personalizada de cada usuario
export const userSettings = learning.table('user_settings', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id).unique(),
  dailyGoal: integer('daily_goal').default(50).notNull(), // Meta diaria de XP
  notificationsEnabled: boolean('notifications_enabled').default(true).notNull(), // Notificaciones activadas
  soundEnabled: boolean('sound_enabled').default(true).notNull(), // Sonido activado
  darkMode: boolean('dark_mode').default(false).notNull(), // Modo oscuro activado
  ...timestamps,
});

// Tabla de sesiones de práctica registradas por el usuario
export const practiceSessions = learning.table('practice_sessions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  subcategoryId: integer('subcategory_id').references(() => subcategories.id), // Subcategoría practicada
  startTime: timestamp('start_time').defaultNow().notNull(), // Inicio
  endTime: timestamp('end_time'), // Fin
  totalXpEarned: integer('total_xp_earned').default(0).notNull(), // XP ganado
  correctAnswers: integer('correct_answers').default(0).notNull(), // Respuestas correctas
  totalQuestions: integer('total_questions').default(0).notNull(), // Preguntas totales
  ...timestamps,
});

// Tabla que registra cada intento de respuesta del usuario
export const attemptHistory = learning.table('attempt_history', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  exerciseId: integer('exercise_id').notNull().references(() => exercises.id),
  userResponse: text('user_response').notNull(), // Respuesta del usuario
  isCorrect: boolean('is_correct').default(false).notNull(), // ¿Fue correcta?
  timeTaken: integer('time_taken').notNull(), // Tiempo tomado
  xpEarned: integer('xp_earned').default(0).notNull(), // XP ganado
  attemptDate: timestamp('attempt_date').defaultNow().notNull(), // Fecha de intento
  ...timestamps,
});

// Tabla de metas diarias del usuario
export const dailyGoals = learning.table('daily_goals', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  date: timestamp('date').notNull(), // Fecha de la meta
  targetXp: integer('target_xp').default(50).notNull(), // XP objetivo
  achievedXp: integer('achieved_xp').default(0).notNull(), // XP logrado
  completed: boolean('completed').default(false).notNull(), // ¿Meta cumplida?
  completedAt: timestamp('completed_at'), // Fecha de cumplimiento
  ...timestamps,
});
import { pgTable, integer, text, timestamp, boolean, primaryKey  } from 'drizzle-orm/pg-core'

const timestamps = {
  updatedAt: timestamp('updated_at'),
  createdAt: timestamp('created_at')
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
  deletedAt: timestamp('deleted_at'),
};
export const users = pgTable('users', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  username: text('username').notNull(),
  password: text('password').notNull(),
  email: text('email').unique(), 
  isActive: boolean('is_active').notNull(), //disponibilidad
  isEmailVerified: boolean('is_email_verified').default(false).notNull(),
  emailVerificationToken: text('email_verification_token'),
  emailVerificationTokenExpires: timestamp('email_verification_token_expires'),
  lastLoginTime: timestamp('last_login_time').defaultNow(), // Tiempo de último inicio de sesión
  ...timestamps,
})

export const persons = pgTable('persons', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  userId: integer('user_id').notNull().references(() => users.id),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  middleName: text('middle_name'),
  email: text('email'),
  alternativeEmail: text('alternative_email'),
  phoneNumber: text('phone_number'),
  mobileNumber: text('mobile_number'),
  address: text('address'),
  gender: text('gender'),
  birthDate: timestamp('birth_date'),
  documentNumber: text('document_number'),
  documentType: text('document_type'),
  ...timestamps,
});

export const roles = pgTable('roles', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: text('name').notNull().unique(),
  description: text('description'),
  ...timestamps,
});

export const permissions = pgTable('permissions', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: text('name').notNull().unique(),
  description: text('description'),
  resource: text('resource').notNull(), // Recurso al que aplica (ej: 'users', 'products')
  action: text('action').notNull(), // Acción permitida (ej: 'read', 'create', 'update', 'delete')
  ...timestamps,
});

export const userRoles = pgTable('user_roles', {
  userId: integer('user_id').notNull().references(() => users.id),
  roleId: integer('role_id').notNull().references(() => roles.id),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.userId, table.roleId] }),
  }
});

export const rolePermissions = pgTable('role_permissions', {
  roleId: integer('role_id').notNull().references(() => roles.id),
  permissionId: integer('permission_id').notNull().references(() => permissions.id),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
  }
});

export const tokens = pgTable('tokens', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  userId: integer('user_id').notNull().references(() => users.id),
  token: text('token').notNull(),
  isRevoked: boolean('is_revoked').notNull().default(false),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
})
import { forwardRef, Modu<PERSON> } from '@nestjs/common';
import { RoleController } from './infrastructure/controllers/role.controller';
import { PermissionController } from './infrastructure/controllers/permission.controller';
import { UserController } from './infrastructure/controllers/user.controller';
import { PersonController } from './infrastructure/controllers/person.controller';

import { CreateRoleUseCase } from './application/usecases/role/create-role.usecase';
import { GetRolesUseCase } from './application/usecases/role/get-roles.usecase';
import { AssignRoleUseCase } from './application/usecases/role/assign-role.usecase';

import { CreatePermissionUseCase } from './application/usecases/permission/create-permission.usecase';
import { GetPermissionsUseCase } from './application/usecases/permission/get-permissions.usecase';
import { AssignPermissionUseCase } from './application/usecases/permission/assign-permission.usecase';

import { CreateUserUseCase } from './application/usecases/user/create-user.usecase';
import { GetUsersUseCase } from './application/usecases/user/get-users.usecase';
import { GetUserUseCase } from './application/usecases/user/get-user.usecase';
import { UpdateUserUseCase } from './application/usecases/user/update-user.usecase';
import { DeleteUserUseCase } from './application/usecases/user/delete-user.usecase';

import { CreatePersonUseCase } from './application/usecases/person/create-person.usecase';
import { GetPersonUseCase } from './application/usecases/person/get-person.usecase';
import { GetPersonByUserUseCase } from './application/usecases/person/get-person-by-user.usecase';
import { UpdatePersonUseCase } from './application/usecases/person/update-person.usecase';

import { RoleRepository } from './infrastructure/repositories/role.repository';
import { PermissionRepository } from './infrastructure/repositories/permission.repository';
import { PermissionGuard } from './infrastructure/guards/permission.guard';
import { AuthModule } from '../auth/auth.module';
import { UserRoleRepository } from './infrastructure/repositories/user-role.repository';

@Module({
  imports: [
    forwardRef(() => AuthModule), // Usar forwardRef para evitar dependencias circulares
  ],
  controllers: [RoleController, PermissionController, UserController, PersonController],
  providers: [
    // Role use cases
    CreateRoleUseCase,
    GetRolesUseCase,
    AssignRoleUseCase,
    
    // Permission use cases
    CreatePermissionUseCase,
    GetPermissionsUseCase,
    AssignPermissionUseCase,
    
    // User use cases
    CreateUserUseCase,
    GetUsersUseCase,
    GetUserUseCase,
    UpdateUserUseCase,
    DeleteUserUseCase,
    
    // Person use cases
    CreatePersonUseCase,
    GetPersonUseCase,
    GetPersonByUserUseCase,
    UpdatePersonUseCase,
    
    // Guards
    PermissionGuard,
    {
      provide: 'IRoleRepository',
      useClass: RoleRepository,
    },
    {
      provide: 'IPermissionRepository',
      useClass: PermissionRepository,
    },
    {
      provide: 'IUserRoleRepository',
      useClass: UserRoleRepository,
    },
  ],
  exports: [
    'IRoleRepository',
    'IPermissionRepository',
    'IUserRoleRepository',
    PermissionGuard
  ]
})
export class RbacModule {}
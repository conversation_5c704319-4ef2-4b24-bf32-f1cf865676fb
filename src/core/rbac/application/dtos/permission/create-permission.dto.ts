import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePermissionDto {
  @ApiProperty({ description: 'Permission name', example: 'create_user' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Permission description', example: 'Allows creating new users', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Resource the permission applies to', example: 'users' })
  @IsNotEmpty()
  @IsString()
  resource: string;

  @ApiProperty({ description: 'Action allowed on the resource', example: 'create' })
  @IsNotEmpty()
  @IsString()
  action: string;
}
import { IsNotEmpty, IsS<PERSON>, <PERSON><PERSON>ptional, IsE<PERSON>, IsDate, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

enum DocumentType {
  ID = 'id',
  PASSPORT = 'passport',
  DRIVER_LICENSE = 'driver_license',
  OTHER = 'other'
}

export class CreatePersonDto {
  @ApiProperty({ description: 'First name', example: 'John' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name', example: 'Doe' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Middle name', example: 'Robert', required: false })
  @IsOptional()
  @IsString()
  middleName?: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ description: 'Alternative email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  alternativeEmail?: string;

  @ApiProperty({ description: 'Phone number', example: '+**********', required: false })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ description: 'Mobile number', example: '+**********', required: false })
  @IsOptional()
  @IsString()
  mobileNumber?: string;

  @ApiProperty({ description: 'Address', example: '123 Main St, City, Country', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Gender', enum: Gender, example: 'male', required: false })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiProperty({ description: 'Birth date', example: '1990-01-01', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  birthDate?: Date;

  @ApiProperty({ description: 'Document number', example: '123456789', required: false })
  @IsOptional()
  @IsString()
  documentNumber?: string;

  @ApiProperty({ description: 'Document type', enum: DocumentType, example: 'id', required: false })
  @IsOptional()
  @IsEnum(DocumentType)
  documentType?: DocumentType;
}
import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserDto {
  @ApiProperty({ description: 'Username', example: 'john.doe', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ description: 'Password', example: 'newSecurePassword123', required: false })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({ description: 'User active status', example: true, required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
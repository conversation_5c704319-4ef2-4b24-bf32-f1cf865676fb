import { IsNotEmpty, <PERSON><PERSON><PERSON>, IsBoolean, Is<PERSON>ptional, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreatePersonDto } from '../person/create-person.dto';

export class CreateUserDto {
  @ApiProperty({ description: 'Username', example: 'john.doe' })
  @IsNotEmpty()
  @IsString()
  username: string;

  @ApiProperty({ description: 'Password', example: 'securePassword123' })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({ description: 'User active status', example: true, default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;

  @ApiProperty({ description: 'Personal information', type: CreatePersonDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CreatePersonDto)
  person: CreatePersonDto;
}
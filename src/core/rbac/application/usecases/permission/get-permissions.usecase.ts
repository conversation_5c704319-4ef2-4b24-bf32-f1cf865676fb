import { Inject, Injectable } from '@nestjs/common';
import { IPermissionRepository } from '../../../domain/ports/permission.repository.port';

@Injectable()
export class GetPermissionsUseCase {
  constructor(
    @Inject('IPermissionRepository')
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  async execute() {
    const permissions = await this.permissionRepository.findAll();
    
    return {
      message: 'Permissions retrieved successfully',
      data: permissions,
    };
  }
}
import { Inject, Injectable, ConflictException } from '@nestjs/common';
import { IPermissionRepository } from '../../../domain/ports/permission.repository.port';
import { CreatePermissionDto } from '../../dtos/permission/create-permission.dto';

@Injectable()
export class CreatePermissionUseCase {
  constructor(
    @Inject('IPermissionRepository')
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  async execute(createPermissionDto: CreatePermissionDto) {
    // Verificar si ya existe un permiso con el mismo nombre
    const existingPermission = await this.permissionRepository.findByName(createPermissionDto.name);
    if (existingPermission) {
      throw new ConflictException({
        message: 'Permission with this name already exists',
        data: null,
      });
    }

    const permission = await this.permissionRepository.create({
      name: createPermissionDto.name,
      description: createPermissionDto.description,
      resource: createPermissionDto.resource,
      action: createPermissionDto.action,
    });

    return {
      message: 'Permission created successfully',
      data: permission,
    };
  }
}
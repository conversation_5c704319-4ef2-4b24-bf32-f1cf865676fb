import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IRoleRepository } from '../../../domain/ports/role.repository.port';
import { IPermissionRepository } from '../../../domain/ports/permission.repository.port';
import { AssignPermissionDto } from '../../dtos/permission/assign-permission.dto';

@Injectable()
export class AssignPermissionUseCase {
  constructor(
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
    @Inject('IPermissionRepository')
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  async execute(roleId: number, assignPermissionDto: AssignPermissionDto) {
    // Verificar si el rol existe
    const role = await this.roleRepository.findById(roleId);
    if (!role) {
      throw new NotFoundException({
        message: 'Role not found',
        data: null,
      });
    }

    // Verificar si el permiso existe
    const permission = await this.permissionRepository.findById(assignPermissionDto.permissionId);
    if (!permission) {
      throw new NotFoundException({
        message: 'Permission not found',
        data: null,
      });
    }

    // Asignar el permiso al rol
    await this.permissionRepository.assignPermissionToRole(roleId, assignPermissionDto.permissionId);

    return {
      message: 'Permission assigned successfully',
      data: {
        roleId,
        roleName: role.name,
        permissionId: assignPermissionDto.permissionId,
        permissionName: permission.name,
      },
    };
  }
}
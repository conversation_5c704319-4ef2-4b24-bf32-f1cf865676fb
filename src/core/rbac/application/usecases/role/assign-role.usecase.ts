import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IRoleRepository } from '../../../domain/ports/role.repository.port';
import { AssignRoleDto } from '../../dtos/role/assign-role.dto';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';

@Injectable()
export class AssignRoleUseCase {
  constructor(
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute(userId: number, assignRoleDto: AssignRoleDto) {
    // Verificar si el usuario existe
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Verificar si el rol existe
    const role = await this.roleRepository.findById(assignRoleDto.roleId);
    if (!role) {
      throw new NotFoundException({
        message: 'Role not found',
        data: null,
      });
    }

    // Asignar el rol al usuario
    await this.roleRepository.assignRoleToUser(userId, assignRoleDto.roleId);

    return {
      message: 'Role assigned successfully',
      data: {
        userId,
        roleId: assignRoleDto.roleId,
        roleName: role.name,
      },
    };
  }
}
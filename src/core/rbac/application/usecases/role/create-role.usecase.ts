import { Inject, Injectable, ConflictException } from '@nestjs/common';
import { IRoleRepository } from '../../../domain/ports/role.repository.port';
import { CreateRoleDto } from '../../dtos/role/create-role.dto';

@Injectable()
export class CreateRoleUseCase {
  constructor(
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
  ) {}

  async execute(createRoleDto: CreateRoleDto) {
    // Verificar si ya existe un rol con el mismo nombre
    const existingRole = await this.roleRepository.findByName(createRoleDto.name);
    if (existingRole) {
      throw new ConflictException({
        message: 'Role with this name already exists',
        data: null,
      });
    }

    const role = await this.roleRepository.create({
      name: createRoleDto.name,
      description: createRoleDto.description,
    });

    return {
      message: 'Role created successfully',
      data: role,
    };
  }
}
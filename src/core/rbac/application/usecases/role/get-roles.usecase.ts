import { Inject, Injectable } from '@nestjs/common';
import { IRoleRepository } from '../../../domain/ports/role.repository.port';

@Injectable()
export class GetRolesUseCase {
  constructor(
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
  ) {}

  async execute() {
    const roles = await this.roleRepository.findAll();
    
    return {
      message: 'Roles retrieved successfully',
      data: roles,
    };
  }
}
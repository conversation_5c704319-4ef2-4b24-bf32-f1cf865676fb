import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IPersonRepository } from '../../../../auth/domain/ports/person.repository.port';

@Injectable()
export class GetPersonByUserUseCase {
  constructor(
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
  ) {}

  async execute(userId: number) {
    const person = await this.personRepository.findByUserId(userId);
    if (!person) {
      throw new NotFoundException({
        message: 'Person not found for this user',
        data: null,
      });
    }

    return {
      message: 'Person retrieved successfully',
      data: person,
    };
  }
}
import { Test, TestingModule } from '@nestjs/testing';
import { GetPersonByUserUseCase } from './get-person-by-user.usecase';
import { NotFoundException } from '@nestjs/common';

describe('GetPersonByUserUseCase', () => {
  let useCase: GetPersonByUserUseCase;
  let mockPersonRepository;

  beforeEach(async () => {
    // Crear un mock del repositorio de personas
    mockPersonRepository = {
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetPersonByUserUseCase,
        {
          provide: 'IPersonRepository',
          useValue: mockPersonRepository,
        },
      ],
    }).compile();

    useCase = module.get<GetPersonByUserUseCase>(GetPersonByUserUseCase);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should return a person when found by user ID', async () => {
    // Arrange: configurar el mock para devolver una persona
    const mockPerson = {
      id: 1,
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    };
    mockPersonRepository.findByUserId.mockResolvedValue(mockPerson);

    // Act: ejecutar el caso de uso
    const result = await useCase.execute(1);

    // Assert: verificar el resultado
    expect(mockPersonRepository.findByUserId).toHaveBeenCalledWith(1);
    expect(result).toEqual({
      message: 'Person retrieved successfully',
      data: mockPerson,
    });
  });

  it('should throw NotFoundException when person not found for user', async () => {
    // Arrange: configurar el mock para devolver null (persona no encontrada)
    mockPersonRepository.findByUserId.mockResolvedValue(null);

    // Act & Assert: verificar que se lanza la excepción
    await expect(useCase.execute(999)).rejects.toThrow(NotFoundException);
    expect(mockPersonRepository.findByUserId).toHaveBeenCalledWith(999);
  });
});
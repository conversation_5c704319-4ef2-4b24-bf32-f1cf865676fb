import { Test, TestingModule } from '@nestjs/testing';
import { GetPersonUseCase } from './get-person.usecase';
import { NotFoundException } from '@nestjs/common';

describe('GetPersonUseCase', () => {
  let useCase: GetPersonUseCase;
  let mockPersonRepository;

  beforeEach(async () => {
    // Crear un mock del repositorio de personas
    mockPersonRepository = {
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetPersonUseCase,
        {
          provide: 'IPersonRepository',
          useValue: mockPersonRepository,
        },
      ],
    }).compile();

    useCase = module.get<GetPersonUseCase>(GetPersonUseCase);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should return a person when found', async () => {
    // Arrange: configurar el mock para devolver una persona
    const mockPerson = {
      id: 1,
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
    };
    mockPersonRepository.findById.mockResolvedValue(mockPerson);

    // Act: ejecutar el caso de uso
    const result = await useCase.execute(1);

    // Assert: verificar el resultado
    expect(mockPersonRepository.findById).toHaveBeenCalledWith(1);
    expect(result).toEqual({
      message: 'Person retrieved successfully',
      data: mockPerson,
    });
  });

  it('should throw NotFoundException when person not found', async () => {
    // Arrange: configurar el mock para devolver null (persona no encontrada)
    mockPersonRepository.findById.mockResolvedValue(null);

    // Act & Assert: verificar que se lanza la excepción
    await expect(useCase.execute(999)).rejects.toThrow(NotFoundException);
  });
});
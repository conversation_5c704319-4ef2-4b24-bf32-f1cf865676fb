import { Inject, Injectable } from '@nestjs/common';
import { IPersonRepository } from '../../../../auth/domain/ports/person.repository.port';
import { CreatePersonDto } from '../../dtos/person/create-person.dto';

@Injectable()
export class CreatePersonUseCase {
  constructor(
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
  ) {}

  async execute(userId: number, createPersonDto: CreatePersonDto) {
    const person = await this.personRepository.create({
      userId,
      ...createPersonDto
    });

    return {
      message: 'Person created successfully',
      data: person,
    };
  }
}
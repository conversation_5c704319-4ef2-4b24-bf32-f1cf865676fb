import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IPersonRepository } from '../../../../auth/domain/ports/person.repository.port';
import { UpdatePersonDto } from '../../dtos/person/update-person.dto';

@Injectable()
export class UpdatePersonUseCase {
  constructor(
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
  ) {}

  async execute(personId: number, updatePersonDto: UpdatePersonDto) {
    const person = await this.personRepository.findById(personId);
    if (!person) {
      throw new NotFoundException({
        message: 'Person not found',
        data: null,
      });
    }

    const updatedPerson = await this.personRepository.update(personId, updatePersonDto);

    return {
      message: 'Person updated successfully',
      data: updatedPerson,
    };
  }
}
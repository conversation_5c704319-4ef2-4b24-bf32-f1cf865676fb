import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IPersonRepository } from '../../../../auth/domain/ports/person.repository.port';

@Injectable()
export class GetPersonUseCase {
  constructor(
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
  ) {}

  async execute(personId: number) {
    const person = await this.personRepository.findById(personId);
    if (!person) {
      throw new NotFoundException({
        message: 'Person not found',
        data: null,
      });
    }

    return {
      message: 'Person retrieved successfully',
      data: person,
    };
  }
}
import { Inject, Injectable } from '@nestjs/common';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';

@Injectable()
export class GetUsersUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute() {
    const users = await this.userRepository.findAll();
    
    // Filtrar información sensible como contraseñas
    const safeUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      isActive: user.isActive,
      lastLoginTime: user.lastLoginTime,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }));
    
    return {
      message: 'Users retrieved successfully',
      data: safeUsers,
    };
  }
}
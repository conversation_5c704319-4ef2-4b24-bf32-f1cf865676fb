import { Inject, Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';
import { IPasswordHasher } from '../../../../auth/domain/ports/password.hasher.port';
import { UpdateUserDto } from '../../dtos/user/update-user.dto';

@Injectable()
export class UpdateUserUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IPasswordHasher')
    private readonly passwordHasher: IPasswordHasher,
  ) {}

  async execute(userId: number, updateUserDto: UpdateUserDto) {
    // Verificar si el usuario existe
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Si se está actualizando el nombre de usuario, verificar que no exista otro con ese nombre
    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUser = await this.userRepository.findByUsername(updateUserDto.username);
      if (existingUser) {
        throw new ConflictException({
          message: 'User with this username already exists',
          data: null,
        });
      }
    }

    // Preparar datos para actualización
    const updateData: any = {};
    
    if (updateUserDto.username) {
      updateData.username = updateUserDto.username;
    }
    
    if (updateUserDto.password) {
      updateData.password = await this.passwordHasher.hash(updateUserDto.password);
    }
    
    if (updateUserDto.isActive !== undefined) {
      updateData.isActive = updateUserDto.isActive;
    }

    // Actualizar usuario
    const updatedUser = await this.userRepository.update(userId, updateData);

    return {
      message: 'User updated successfully',
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        isActive: updatedUser.isActive,
        updatedAt: updatedUser.updatedAt,
      },
    };
  }
}
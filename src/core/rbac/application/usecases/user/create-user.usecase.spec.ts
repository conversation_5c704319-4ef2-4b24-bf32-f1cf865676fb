import { Test, TestingModule } from '@nestjs/testing';
import { CreateUserUseCase } from './create-user.usecase';
import { ConflictException } from '@nestjs/common';

describe('CreateUserUseCase', () => {
  let useCase: CreateUserUseCase;
  let mockUserRepository;
  let mockPasswordHasher;
  let mockPersonRepository;

  beforeEach(async () => {
    // Crear mocks de los repositorios y servicios
    mockUserRepository = {
      findByUsername: jest.fn(),
      create: jest.fn(),
    };
    
    mockPasswordHasher = {
      hash: jest.fn(),
    };
    
    mockPersonRepository = {
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateUserUseCase,
        {
          provide: 'IUserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'IPasswordHasher',
          useValue: mockPasswordHasher,
        },
        {
          provide: 'IPersonRepository',
          useValue: mockPersonRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateUserUseCase>(CreateUserUseCase);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should create a user and person successfully', async () => {
    // Arrange: configurar los mocks
    mockUserRepository.findByUsername.mockResolvedValue(null); // Usuario no existe
    mockPasswordHasher.hash.mockResolvedValue('hashed_password');
    
    const mockUser = {
      id: 1,
      username: 'testuser',
      isActive: true,
      createdAt: new Date(),
    };
    mockUserRepository.create.mockResolvedValue(mockUser);
    
    const mockPerson = {
      id: 1,
      userId: 1,
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
    };
    mockPersonRepository.create.mockResolvedValue(mockPerson);
    
    // Datos de entrada
    const createUserDto = {
      username: 'testuser',
      password: 'password123',
      isActive: true,
      person: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      }
    };

    // Act: ejecutar el caso de uso
    const result = await useCase.execute(createUserDto);

    // Assert: verificar el resultado
    expect(mockUserRepository.findByUsername).toHaveBeenCalledWith('testuser');
    expect(mockPasswordHasher.hash).toHaveBeenCalledWith('password123');
    expect(mockUserRepository.create).toHaveBeenCalledWith({
      username: 'testuser',
      password: 'hashed_password',
      isActive: true,
    });
    expect(mockPersonRepository.create).toHaveBeenCalledWith(expect.objectContaining({
      userId: 1,
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
    }));
    
    expect(result).toEqual({
      message: 'User created successfully',
      data: expect.objectContaining({
        id: 1,
        username: 'testuser',
        isActive: true,
      }),
    });
  });

  it('should throw ConflictException when username already exists', async () => {
    // Arrange: configurar el mock para devolver un usuario existente
    mockUserRepository.findByUsername.mockResolvedValue({ id: 1, username: 'testuser' });
    
    // Datos de entrada
    const createUserDto = {
      username: 'testuser',
      password: 'password123',
      person: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      }
    };

    // Act & Assert: verificar que se lanza la excepción
    await expect(useCase.execute(createUserDto)).rejects.toThrow(ConflictException);
  });
});
import { Inject, Injectable, ConflictException } from '@nestjs/common';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';
import { IPasswordHasher } from '../../../../auth/domain/ports/password.hasher.port';
import { IPersonRepository } from '../../../../auth/domain/ports/person.repository.port';
import { CreateUserDto } from '../../dtos/user/create-user.dto';

@Injectable()
export class CreateUserUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IPasswordHasher')
    private readonly passwordHasher: IPasswordHasher,
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
  ) {}

  async execute(createUserDto: CreateUserDto) {
    // Verificar si ya existe un usuario con el mismo nombre
    const existingUser = await this.userRepository.findByUsername(createUserDto.username);
    if (existingUser) {
      throw new ConflictException({
        message: 'User with this username already exists',
        data: null,
      });
    }

    // Hashear la contraseña
    const hashedPassword = await this.passwordHasher.hash(createUserDto.password);

    // Crear el usuario
    const user = await this.userRepository.create({
      username: createUserDto.username,
      password: hashedPassword,
      isActive: createUserDto.isActive ?? true,
    });

    // Crear la información personal asociada al usuario
    const person = await this.personRepository.create({
      userId: user.id,
      firstName: createUserDto.person.firstName,
      lastName: createUserDto.person.lastName,
      middleName: createUserDto.person.middleName,
      email: createUserDto.person.email,
      alternativeEmail: createUserDto.person.alternativeEmail,
      phoneNumber: createUserDto.person.phoneNumber,
      mobileNumber: createUserDto.person.mobileNumber,
      address: createUserDto.person.address,
      gender: createUserDto.person.gender,
      birthDate: createUserDto.person.birthDate,
      documentNumber: createUserDto.person.documentNumber,
      documentType: createUserDto.person.documentType,
    });

    return {
      message: 'User created successfully',
      data: {
        id: user.id,
        username: user.username,
        isActive: user.isActive,
        createdAt: user.createdAt,
        person: {
          id: person.id,
          firstName: person.firstName,
          lastName: person.lastName,
          email: person.email,
        }
      },
    };
  }
}
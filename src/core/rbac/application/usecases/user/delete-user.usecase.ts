import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';

@Injectable()
export class DeleteUserUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute(userId: number) {
    // Verificar si el usuario existe
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Eliminar usuario (soft delete)
    await this.userRepository.delete(userId);

    return {
      message: 'User deleted successfully',
      data: { id: userId },
    };
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../../../auth/domain/ports/user.repository.port';
import { IRoleRepository } from '../../../domain/ports/role.repository.port';

@Injectable()
export class GetUserUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
  ) {}

  async execute(userId: number) {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Obtener roles del usuario
    const roles = await this.roleRepository.getUserRoles(userId);
    
    return {
      message: 'User retrieved successfully',
      data: {
        id: user.id,
        username: user.username,
        isActive: user.isActive,
        lastLoginTime: user.lastLoginTime,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roles: roles.map(role => ({
          id: role.id,
          name: role.name,
          description: role.description,
        })),
      },
    };
  }
}
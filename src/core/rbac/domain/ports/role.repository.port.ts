import { Role } from '../entities/role.entity';

export interface IRoleRepository {
  findById(id: number): Promise<Role | null>;
  findByName(name: string): Promise<Role | null>;
  findAll(): Promise<Role[]>;
  create(role: Partial<Role>): Promise<Role>;
  update(id: number, role: Partial<Role>): Promise<Role | null>;
  delete(id: number): Promise<boolean>;
  getUserRoles(userId: number): Promise<Role[]>;
  assignRoleToUser(userId: number, roleId: number): Promise<void>;
  removeRoleFromUser(userId: number, roleId: number): Promise<void>;
}
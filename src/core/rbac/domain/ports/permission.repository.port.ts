import { Permission } from '../entities/permission.entity';

export interface IPermissionRepository {
  findById(id: number): Promise<Permission | null>;
  findByName(name: string): Promise<Permission | null>;
  findAll(): Promise<Permission[]>;
  create(permission: Partial<Permission>): Promise<Permission>;
  update(id: number, permission: Partial<Permission>): Promise<Permission | null>;
  delete(id: number): Promise<boolean>;
  getRolePermissions(roleId: number): Promise<Permission[]>;
  assignPermissionToRole(roleId: number, permissionId: number): Promise<void>;
  removePermissionFromRole(roleId: number, permissionId: number): Promise<void>;
  getUserPermissions(userId: number): Promise<Permission[]>;
}
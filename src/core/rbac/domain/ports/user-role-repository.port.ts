export interface IUserRoleRepository {
    /**
     * Crea una nueva asignación de rol a usuario
     * @param data Objeto con userId y roleId
     */
    create(data: { userId: number; roleId: number }): Promise<void>;
    
    /**
     * Elimina una asignación de rol a usuario
     * @param userId ID del usuario
     * @param roleId ID del rol
     */
    delete(userId: number, roleId: number): Promise<void>;
    
    /**
     * Verifica si un usuario tiene un rol específico
     * @param userId ID del usuario
     * @param roleId ID del rol
     */
    exists(userId: number, roleId: number): Promise<boolean>;
    
    /**
     * Obtiene todos los roles asignados a un usuario
     * @param userId ID del usuario
     */
    findByUserId(userId: number): Promise<number[]>;
    
    /**
     * Obtiene todos los usuarios asignados a un rol
     * @param roleId ID del rol
     */
    findByRoleId(roleId: number): Promise<number[]>;
  }
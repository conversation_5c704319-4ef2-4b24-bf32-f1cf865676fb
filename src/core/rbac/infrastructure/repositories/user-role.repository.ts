import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { userRoles } from '../../../../database/schemas/auth.schema';
import { eq, and, sql } from 'drizzle-orm';
import { IUserRoleRepository } from '@core/rbac/domain/ports/user-role-repository.port';


@Injectable()
export class UserRoleRepository implements IUserRoleRepository {
  async create(data: { userId: number; roleId: number }): Promise<void> {
    await db.insert(userRoles)
      .values({
        userId: data.userId,
        roleId: data.roleId
      })
      .onConflictDoNothing();
  }

  async delete(userId: number, roleId: number): Promise<void> {
    await db.delete(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.roleId, roleId)
        )
      );
  }

  async exists(userId: number, roleId: number): Promise<boolean> {
    // Usar sql.count() en lugar de db.fn.count()
    const result = await db.select({ 
      count: sql<number>`count(*)` 
    })
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.roleId, roleId)
        )
      );
    
    return Number(result[0].count) > 0;
  }

  async findByUserId(userId: number): Promise<number[]> {
    const result = await db.select({ roleId: userRoles.roleId })
      .from(userRoles)
      .where(eq(userRoles.userId, userId));
    
    return result.map(r => r.roleId);
  }

  async findByRoleId(roleId: number): Promise<number[]> {
    const result = await db.select({ userId: userRoles.userId })
      .from(userRoles)
      .where(eq(userRoles.roleId, roleId));
    
    return result.map(r => r.userId);
  }
}
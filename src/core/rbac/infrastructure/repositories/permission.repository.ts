import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { permissions, rolePermissions, userRoles } from '../../../../database/schemas/auth.schema';
import { eq, and, inArray } from 'drizzle-orm';
import { IPermissionRepository } from '../../domain/ports/permission.repository.port';
import { Permission } from '../../domain/entities/permission.entity';

@Injectable()
export class PermissionRepository implements IPermissionRepository {
  async findById(id: number): Promise<Permission | null> {
    const result = await db
      .select()
      .from(permissions)
      .where(eq(permissions.id, id))
      .limit(1);

    if (!result.length) return null;

    const permission = result[0];
    return new Permission(
      permission.id,
      permission.name,
      permission.description || '',
      permission.resource,
      permission.action,
      permission.updatedAt || undefined,
      permission.createdAt,
      permission.deletedAt || undefined
    );
  }

  async findByName(name: string): Promise<Permission | null> {
    const result = await db
      .select()
      .from(permissions)
      .where(eq(permissions.name, name))
      .limit(1);

    if (!result.length) return null;

    const permission = result[0];
    return new Permission(
      permission.id,
      permission.name,
      permission.description || '',
      permission.resource,
      permission.action,
      permission.updatedAt || undefined,
      permission.createdAt,
      permission.deletedAt || undefined
    );
  }

  async findAll(): Promise<Permission[]> {
    const result = await db.select().from(permissions);
    
    return result.map(permission => new Permission(
      permission.id,
      permission.name,
      permission.description || '',
      permission.resource,
      permission.action,
      permission.updatedAt || undefined,
      permission.createdAt,
      permission.deletedAt || undefined
    ));
  }

  async create(permission: Partial<Permission>): Promise<Permission> {
    const result = await db.insert(permissions).values({
      name: permission.name!,
      description: permission.description,
      resource: permission.resource!,
      action: permission.action!,
      createdAt: new Date(),
    }).returning();

    const newPermission = result[0];
    return new Permission(
      newPermission.id,
      newPermission.name,
      newPermission.description || '',
      newPermission.resource,
      newPermission.action,
      newPermission.updatedAt || undefined,
      newPermission.createdAt,
      newPermission.deletedAt || undefined
    );
  }

  async update(id: number, permission: Partial<Permission>): Promise<Permission | null> {
    const result = await db.update(permissions)
      .set({
        name: permission.name,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
        updatedAt: new Date(),
      })
      .where(eq(permissions.id, id))
      .returning();

    if (!result.length) return null;

    const updatedPermission = result[0];
    return new Permission(
      updatedPermission.id,
      updatedPermission.name,
      updatedPermission.description || '',
      updatedPermission.resource,
      updatedPermission.action,
      updatedPermission.updatedAt || undefined,
      updatedPermission.createdAt,
      updatedPermission.deletedAt || undefined
    );
  }

  async delete(id: number): Promise<boolean> {
    const result = await db.update(permissions)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(permissions.id, id))
      .returning();

    return result.length > 0;
  }

  async getRolePermissions(roleId: number): Promise<Permission[]> {
    const result = await db
      .select({
        permission: permissions
      })
      .from(rolePermissions)
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(eq(rolePermissions.roleId, roleId));

    return result.map(({ permission }) => new Permission(
      permission.id,
      permission.name,
      permission.description || '',
      permission.resource,
      permission.action,
      permission.updatedAt || undefined,
      permission.createdAt,
      permission.deletedAt || undefined
    ));
  }

  async assignPermissionToRole(roleId: number, permissionId: number): Promise<void> {
    await db.insert(rolePermissions)
      .values({ roleId, permissionId })
      .onConflictDoNothing();
  }

  async removePermissionFromRole(roleId: number, permissionId: number): Promise<void> {
    await db.delete(rolePermissions)
      .where(and(
        eq(rolePermissions.roleId, roleId),
        eq(rolePermissions.permissionId, permissionId)
      ));
  }

  async getUserPermissions(userId: number): Promise<Permission[]> {
    // Obtener todos los roles del usuario
    const userRolesResult = await db
      .select({ roleId: userRoles.roleId })
      .from(userRoles)
      .where(eq(userRoles.userId, userId));
    
    if (!userRolesResult.length) return [];
    
    const roleIds = userRolesResult.map(r => r.roleId);
    
    // Obtener todos los permisos asociados a esos roles
    const result = await db
      .select({ permission: permissions })
      .from(rolePermissions)
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(inArray(rolePermissions.roleId, roleIds));
    
    // Convertir los resultados a entidades Permission
    return result.map(({ permission }) => new Permission(
      permission.id,
      permission.name,
      permission.description || '',
      permission.resource,
      permission.action,
      permission.updatedAt || undefined,
      permission.createdAt,
      permission.deletedAt || undefined
    ));
  }
}
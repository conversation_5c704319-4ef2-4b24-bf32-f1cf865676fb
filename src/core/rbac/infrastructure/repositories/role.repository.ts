import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { roles, userRoles } from '../../../../database/schemas/auth.schema';
import { eq, and } from 'drizzle-orm';
import { IRoleRepository } from '../../domain/ports/role.repository.port';
import { Role } from '../../domain/entities/role.entity';

@Injectable()
export class RoleRepository implements IRoleRepository {
  async findById(id: number): Promise<Role | null> {
    const result = await db
      .select()
      .from(roles)
      .where(eq(roles.id, id))
      .limit(1);

    if (!result.length) return null;

    const role = result[0];
    return new Role(
      role.id,
      role.name,
      role.description || undefined,
      role.updatedAt || undefined,
      role.createdAt,
      role.deletedAt || undefined
    );
  }

  async findByName(name: string): Promise<Role | null> {
    const result = await db
      .select()
      .from(roles)
      .where(eq(roles.name, name))
      .limit(1);

    if (!result.length) return null;

    const role = result[0];
    return new Role(
      role.id,
      role.name,
      role.description || undefined,
      role.updatedAt || undefined,
      role.createdAt,
      role.deletedAt || undefined
    );
  }

  async findAll(): Promise<Role[]> {
    const result = await db.select().from(roles);
    
    return result.map(role => new Role(
      role.id,
      role.name,
      role.description || undefined,
      role.updatedAt || undefined,
      role.createdAt,
      role.deletedAt || undefined
    ));
  }

  async create(role: Partial<Role>): Promise<Role> {
    const result = await db.insert(roles).values({
      name: role.name!,
      description: role.description,
      createdAt: new Date(),
    }).returning();

    const newRole = result[0];
    return new Role(
      newRole.id,
      newRole.name,
      newRole.description || undefined,
      newRole.updatedAt || undefined,
      newRole.createdAt,
      newRole.deletedAt || undefined
    );
  }

  async update(id: number, role: Partial<Role>): Promise<Role | null> {
    const result = await db.update(roles)
      .set({
        name: role.name,
        description: role.description,
        updatedAt: new Date(),
      })
      .where(eq(roles.id, id))
      .returning();

    if (!result.length) return null;

    const updatedRole = result[0];
    return new Role(
      updatedRole.id,
      updatedRole.name,
      updatedRole.description || undefined,
      updatedRole.updatedAt || undefined,
      updatedRole.createdAt,
      updatedRole.deletedAt || undefined
    );
  }

  async delete(id: number): Promise<boolean> {
    const result = await db.update(roles)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(roles.id, id))
      .returning();

    return result.length > 0;
  }

  async getUserRoles(userId: number): Promise<Role[]> {
    const result = await db
      .select({
        role: roles
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .where(eq(userRoles.userId, userId));

    return result.map(({ role }) => new Role(
      role.id,
      role.name,
      role.description || undefined,
      role.updatedAt || undefined,
      role.createdAt,
      role.deletedAt || undefined
    ));
  }

  async assignRoleToUser(userId: number, roleId: number): Promise<void> {
    await db.insert(userRoles)
      .values({ userId, roleId })
      .onConflictDoNothing();
  }

  async removeRoleFromUser(userId: number, roleId: number): Promise<void> {
    await db.delete(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.roleId, roleId)
      ));
  }
}
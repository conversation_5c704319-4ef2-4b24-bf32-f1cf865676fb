import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IPermissionRepository } from '../../domain/ports/permission.repository.port';
import { ITokenService } from '../../../../core/auth/domain/ports/token.service.port';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject('IPermissionRepository')
    private readonly permissionRepository: IPermissionRepository,
    @Inject('ITokenService')
    private readonly tokenService: ITokenService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler(),
    );
  
    //console.log('Required permissions:', requiredPermissions);
  
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // No se requiere permiso específico
    }
  
    const request = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.split(' ')[1];
    
    if (!token) {
      throw new ForbiddenException({
        message: 'No token provided',
        data: null,
      });
    }
  
    try {
      // Obtener el ID del usuario del token
      const userId = await this.tokenService.getUserIdFromToken(token);
      //console.log('User ID:', userId);
      
      // Obtener los permisos del usuario
      const userPermissions = await this.permissionRepository.getUserPermissions(userId);
      //console.log('User permissions:', userPermissions);
      
      // Verificar si el usuario tiene alguno de los permisos requeridos
      for (const requiredPermission of requiredPermissions) {
        //console.log('Checking permission:', requiredPermission);
        //const [resource, action] = requiredPermission.split(':');
        const parts = requiredPermission.split(':');
        const action = parts.pop();
        const resource = parts.join(':');
        //console.log(action, resource);
        
        const hasPermission = userPermissions.some(
          permission => 
            permission.resource === resource && 
            permission.action === action
        );
        
        //console.log('Has permission:', hasPermission);
        
        if (hasPermission) {
          return true;
        }
      }
      
      throw new ForbiddenException({
        message: 'Insufficient permissions',
        data: null,
      });
    } catch (error) {
      //console.error('Permission error:', error);
      throw new ForbiddenException({
        message: error.message || 'Permission denied',
        data: null,
      });
    }
  }
}
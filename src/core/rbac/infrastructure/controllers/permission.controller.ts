import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CreatePermissionDto } from '../../application/dtos/permission/create-permission.dto';
import { AssignPermissionDto } from '../../application/dtos/permission/assign-permission.dto';
import { CreatePermissionUseCase } from '../../application/usecases/permission/create-permission.usecase';
import { GetPermissionsUseCase } from '../../application/usecases/permission/get-permissions.usecase';
import { AssignPermissionUseCase } from '../../application/usecases/permission/assign-permission.usecase';
import { AuthGuard as JwtAuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { RequirePermissions } from '../decorators/require-permissions.decorator';
import { PermissionGuard } from '../guards/permission.guard';

@ApiTags('permissions')
@Controller('permissions')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class PermissionController {
  constructor(
    private readonly createPermissionUseCase: CreatePermissionUseCase,
    private readonly getPermissionsUseCase: GetPermissionsUseCase,
    private readonly assignPermissionUseCase: AssignPermissionUseCase,
  ) {}

  @Post()
  @RequirePermissions('permissions:create')
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiBody({ type: CreatePermissionDto })
  @ApiResponse({ status: 201, description: 'Permission created successfully' })
  @ApiResponse({ status: 409, description: 'Permission with this name already exists' })
  async createPermission(@Body() createPermissionDto: CreatePermissionDto) {
    return this.createPermissionUseCase.execute(createPermissionDto);
  }

  @Get()
  @RequirePermissions('permissions:read')
  @ApiOperation({ summary: 'Get all permissions' })
  @ApiResponse({ status: 200, description: 'Permissions retrieved successfully' })
  async getPermissions() {
    return this.getPermissionsUseCase.execute();
  }

  @Post('roles/:roleId/assign')
  @RequirePermissions('permissions:assign')
  @ApiOperation({ summary: 'Assign a permission to a role' })
  @ApiBody({ type: AssignPermissionDto })
  @ApiResponse({ status: 200, description: 'Permission assigned successfully' })
  @ApiResponse({ status: 404, description: 'Role or permission not found' })
  async assignPermission(
    @Param('roleId') roleId: number,
    @Body() assignPermissionDto: AssignPermissionDto,
  ) {
    return this.assignPermissionUseCase.execute(roleId, assignPermissionDto);
  }
}
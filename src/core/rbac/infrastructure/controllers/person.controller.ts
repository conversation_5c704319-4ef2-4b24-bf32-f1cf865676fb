import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CreatePersonUseCase } from '../../application/usecases/person/create-person.usecase';
import { GetPersonUseCase } from '../../application/usecases/person/get-person.usecase';
import { GetPersonByUserUseCase } from '../../application/usecases/person/get-person-by-user.usecase';
import { UpdatePersonUseCase } from '../../application/usecases/person/update-person.usecase';
import { CreatePersonDto } from '../../application/dtos/person/create-person.dto';
import { UpdatePersonDto } from '../../application/dtos/person/update-person.dto';
import { AuthGuard as JwtAuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { RequirePermissions } from '../decorators/require-permissions.decorator';
import { PermissionGuard } from '../guards/permission.guard';

@ApiTags('persons')
@Controller('persons')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class PersonController {
  constructor(
    private readonly createPersonUseCase: CreatePersonUseCase,
    private readonly getPersonUseCase: GetPersonUseCase,
    private readonly getPersonByUserUseCase: GetPersonByUserUseCase,
    private readonly updatePersonUseCase: UpdatePersonUseCase,
  ) {}

  @Post('user/:userId')
  @RequirePermissions('persons:create')
  @ApiOperation({ summary: 'Create a new person for a user' })
  @ApiBody({ type: CreatePersonDto })
  @ApiResponse({ status: 201, description: 'Person created successfully' })
  async createPerson(
    @Param('userId') userId: number,
    @Body() createPersonDto: CreatePersonDto
  ) {
    return this.createPersonUseCase.execute(userId, createPersonDto);
  }

  @Get(':id')
  @RequirePermissions('persons:read')
  @ApiOperation({ summary: 'Get a person by ID' })
  @ApiResponse({ status: 200, description: 'Person retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Person not found' })
  async getPerson(@Param('id') id: number) {
    return this.getPersonUseCase.execute(id);
  }

  @Get('user/:userId')
  @RequirePermissions('persons:read')
  @ApiOperation({ summary: 'Get a person by user ID' })
  @ApiResponse({ status: 200, description: 'Person retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Person not found for this user' })
  async getPersonByUser(@Param('userId') userId: number) {
    return this.getPersonByUserUseCase.execute(userId);
  }

  @Put(':id')
  @RequirePermissions('persons:update')
  @ApiOperation({ summary: 'Update a person' })
  @ApiBody({ type: UpdatePersonDto })
  @ApiResponse({ status: 200, description: 'Person updated successfully' })
  @ApiResponse({ status: 404, description: 'Person not found' })
  async updatePerson(
    @Param('id') id: number,
    @Body() updatePersonDto: UpdatePersonDto
  ) {
    return this.updatePersonUseCase.execute(id, updatePersonDto);
  }
}
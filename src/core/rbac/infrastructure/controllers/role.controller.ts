import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CreateRoleDto } from '../../application/dtos/role/create-role.dto';
import { AssignRoleDto } from '../../application/dtos/role/assign-role.dto';
import { CreateRoleUseCase } from '../../application/usecases/role/create-role.usecase';
import { GetRolesUseCase } from '../../application/usecases/role/get-roles.usecase';
import { AssignRoleUseCase } from '../../application/usecases/role/assign-role.usecase';
import { AuthGuard as JwtAuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { RequirePermissions } from '../decorators/require-permissions.decorator';
import { PermissionGuard } from '../guards/permission.guard';

@ApiTags('roles')
@Controller('roles')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class RoleController {
  constructor(
    private readonly createRoleUseCase: CreateRoleUseCase,
    private readonly getRolesUseCase: GetRolesUseCase,
    private readonly assignRoleUseCase: AssignRoleUseCase,
  ) {}

  @Post()
  @RequirePermissions('roles:create')
  @ApiOperation({ summary: 'Create a new role' })
  @ApiBody({ type: CreateRoleDto })
  @ApiResponse({ status: 201, description: 'Role created successfully' })
  @ApiResponse({ status: 409, description: 'Role with this name already exists' })
  async createRole(@Body() createRoleDto: CreateRoleDto) {
    return this.createRoleUseCase.execute(createRoleDto);
  }

  @Get()
  @RequirePermissions('roles:read')
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'Roles retrieved successfully' })
  async getRoles() {
    return this.getRolesUseCase.execute();
  }

  @Post('users/:userId/assign')
  @RequirePermissions('roles:assign')
  @ApiOperation({ summary: 'Assign a role to a user' })
  @ApiBody({ type: AssignRoleDto })
  @ApiResponse({ status: 200, description: 'Role assigned successfully' })
  @ApiResponse({ status: 404, description: 'User or role not found' })
  async assignRole(
    @Param('userId') userId: number,
    @Body() assignRoleDto: AssignRoleDto,
  ) {
    return this.assignRoleUseCase.execute(userId, assignRoleDto);
  }
}
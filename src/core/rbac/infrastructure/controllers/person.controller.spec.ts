import { Test, TestingModule } from '@nestjs/testing';
import { Person<PERSON>ontroller } from './person.controller';
import { CreatePersonUseCase } from '../../application/usecases/person/create-person.usecase';
import { GetPersonUseCase } from '../../application/usecases/person/get-person.usecase';
import { GetPersonByUserUseCase } from '../../application/usecases/person/get-person-by-user.usecase';
import { UpdatePersonUseCase } from '../../application/usecases/person/update-person.usecase';
import { AuthGuard } from '../../../../core/auth/infrastructure/guards/auth.guard';
import { PermissionGuard } from '../guards/permission.guard';

describe('PersonController', () => {
  let controller: PersonController;
  let mockCreatePersonUseCase;
  let mockGetPersonUseCase;
  let mockGetPersonByUserUseCase;
  let mockUpdatePersonUseCase;

  beforeEach(async () => {
    // Crear mocks de los casos de uso
    mockCreatePersonUseCase = {
      execute: jest.fn(),
    };
    
    mockGetPersonUseCase = {
      execute: jest.fn(),
    };
    
    mockGetPersonByUserUseCase = {
      execute: jest.fn(),
    };
    
    mockUpdatePersonUseCase = {
      execute: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PersonController],
      providers: [
        {
          provide: CreatePersonUseCase,
          useValue: mockCreatePersonUseCase,
        },
        {
          provide: GetPersonUseCase,
          useValue: mockGetPersonUseCase,
        },
        {
          provide: GetPersonByUserUseCase,
          useValue: mockGetPersonByUserUseCase,
        },
        {
          provide: UpdatePersonUseCase,
          useValue: mockUpdatePersonUseCase,
        },
      ],
    })
    // Sobrescribir los guards para las pruebas
    .overrideGuard(AuthGuard)
    .useValue({ canActivate: () => true })
    .overrideGuard(PermissionGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<PersonController>(PersonController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a person', async () => {
    // Arrange
    const userId = 1;
    const createPersonDto = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
    };
    
    const expectedResult = {
      message: 'Person created successfully',
      data: {
        id: 1,
        userId: 1,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      },
    };
    
    mockCreatePersonUseCase.execute.mockResolvedValue(expectedResult);

    // Act
    const result = await controller.createPerson(userId, createPersonDto);

    // Assert
    expect(mockCreatePersonUseCase.execute).toHaveBeenCalledWith(userId, createPersonDto);
    expect(result).toEqual(expectedResult);
  });

  it('should get a person by ID', async () => {
    // Arrange
    const personId = 1;
    const expectedResult = {
      message: 'Person retrieved successfully',
      data: {
        id: 1,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      },
    };
    
    mockGetPersonUseCase.execute.mockResolvedValue(expectedResult);

    // Act
    const result = await controller.getPerson(personId);

    // Assert
    expect(mockGetPersonUseCase.execute).toHaveBeenCalledWith(personId);
    expect(result).toEqual(expectedResult);
  });

  it('should get a person by user ID', async () => {
    // Arrange
    const userId = 1;
    const expectedResult = {
      message: 'Person retrieved successfully',
      data: {
        id: 1,
        userId: 1,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      },
    };
    
    mockGetPersonByUserUseCase.execute.mockResolvedValue(expectedResult);

    // Act
    const result = await controller.getPersonByUser(userId);

    // Assert
    expect(mockGetPersonByUserUseCase.execute).toHaveBeenCalledWith(userId);
    expect(result).toEqual(expectedResult);
  });

  it('should update a person', async () => {
    // Arrange
    const personId = 1;
    const updatePersonDto = {
      firstName: 'Updated',
      lastName: 'User',
      email: '<EMAIL>',
    };
    
    const expectedResult = {
      message: 'Person updated successfully',
      data: {
        id: 1,
        firstName: 'Updated',
        lastName: 'User',
        email: '<EMAIL>',
      },
    };
    
    mockUpdatePersonUseCase.execute.mockResolvedValue(expectedResult);

    // Act
    const result = await controller.updatePerson(personId, updatePersonDto);

    // Assert
    expect(mockUpdatePersonUseCase.execute).toHaveBeenCalledWith(personId, updatePersonDto);
    expect(result).toEqual(expectedResult);
  });
});
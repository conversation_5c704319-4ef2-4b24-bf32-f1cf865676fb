import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const GetUser = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    if(!user) return undefined;

    if(!data)  return user;

    if(data === 'id' &&  user.sub !== undefined) return user.sub;

    return user[data];
  },
);
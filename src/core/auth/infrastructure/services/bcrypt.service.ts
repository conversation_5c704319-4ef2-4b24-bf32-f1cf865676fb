import { Injectable } from '@nestjs/common';
import { IPasswordHasher } from '../../domain/ports/password.hasher.port';
import * as bcrypt from 'bcrypt';

@Injectable()
export class BcryptService implements IPasswordHasher {
  async hash(plainPassword: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(plainPassword, salt);
  }

  async compare(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
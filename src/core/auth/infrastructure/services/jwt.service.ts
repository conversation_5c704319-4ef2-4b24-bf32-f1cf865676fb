import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { ITokenService, TokenPayload } from '../../domain/ports/token.service.port';
import { ConfigService } from '@nestjs/config';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import { ITokenRepository } from '../../domain/ports/token.repository.port';

@Injectable()
export class JwtService implements ITokenService {
  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: NestJwtService,
    @Inject('ITokenRepository')
    private readonly tokenRepository: ITokenRepository,
  ) {
    // Programar limpieza de tokens expirados cada día
    setInterval(() => {
      this.tokenRepository.cleanupExpiredTokens().catch(err => 
        console.error('Error cleaning up expired tokens:', err)
      );
    }, 24 * 60 * 60 * 1000); // 24 horas
  }

  async generate(payload: TokenPayload): Promise<string> {
    const expiresIn = '1d';
    const token = this.jwtService.sign(payload);
    
    // Calcular la fecha de expiración
    const expiresInMs = 24 * 60 * 60 * 1000; // 1 día en milisegundos
    const expiresAt = new Date(Date.now() + expiresInMs);
    
    // Guardar el token en la base de datos
    await this.tokenRepository.save({
      userId: payload.sub,
      token,
      isRevoked: false,
      expiresAt,
    });
    
    return token;
  }

  async validate(token: string): Promise<TokenPayload> {
    // Verificar si el token existe en la base de datos
    const tokenData = await this.tokenRepository.findByToken(token);
    if (!tokenData) {
      throw new UnauthorizedException({
        message: 'Token not found',
        data: null,
      });
    }
    
    // Verificar si el token ha sido revocado
    if (tokenData.isRevoked) {
      throw new UnauthorizedException({
        message: 'Token has been revoked',
        data: null,
      });
    }
    
    // Verificar si el token ha expirado
    if (tokenData.expiresAt < new Date()) {
      throw new UnauthorizedException({
        message: 'Token has expired',
        data: null,
      });
    }
    
    try {
      // Verificar la firma del token
      return this.jwtService.verify<TokenPayload>(token);
    } catch (error) {
      throw new UnauthorizedException({
        message: 'Invalid token',
        data: null,
      });
    }
  }

  async invalidate(token: string): Promise<void> {
    await this.tokenRepository.revokeToken(token);
  }

  async getUserIdFromToken(token: string): Promise<number> {
    const payload = await this.validate(token);
    return payload.sub;
  }

  async invalidateUserSessions(userId: number): Promise<void> {
    await this.tokenRepository.revokeAllUserTokens(userId);
  }
}
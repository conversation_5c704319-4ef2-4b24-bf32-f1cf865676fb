import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
    Inject,
  } from '@nestjs/common';
  import { Request } from 'express';
  import { ITokenService } from '../../domain/ports/token.service.port';
  
  @Injectable()
  export class AuthGuard implements CanActivate {
    constructor(
      @Inject('ITokenService')
      private readonly tokenService: ITokenService,
    ) {}
  
    async canActivate(context: ExecutionContext): Promise<boolean> {
      const request = context.switchToHttp().getRequest();
      const token = this.extractTokenFromHeader(request);
      
      if (!token) {
        throw new UnauthorizedException({
          message: 'No token found',
          data: null,
          redirect: '/login',
        });
      }
      
      try {
        const payload = await this.tokenService.validate(token);
        request['user'] = payload;
        return true;
      } catch (error) {
        throw new UnauthorizedException({
          message: error.message || 'Invalid token',
          data: null,
          redirect: '/login',
        });
      }
    }
  
    private extractTokenFromHeader(request: Request): string | undefined {
      const [type, token] = request.headers.authorization?.split(' ') ?? [];
      return type === 'Bearer' ? token : undefined;
    }
  }

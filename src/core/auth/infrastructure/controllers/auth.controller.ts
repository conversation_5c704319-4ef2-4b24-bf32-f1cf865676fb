import { Body, Controller, Get, Param, Patch, Post, Query, Req, Res, UseGuards } from '@nestjs/common';
import { LoginDto } from '../../application/dtos/login.dto';
import { LogoutDto } from '../../application/dtos/logout.dto';
import { LoginUseCase } from '../../application/usecases/login.usecase';
import { LogoutUseCase } from '../../application/usecases/logout.usecase';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { AuthGuard as JwtAuthGuard } from '../guards/auth.guard';
import { ToggleUserStatusUseCase } from '../../application/usecases/toggle-user-status.usecase';
import { RegisterUseCase } from '@auth/application/usecases/register.usecase';
import { RegisterDto } from '@auth/application/dtos/register.dto';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';
import { GoogleAuthUseCase } from '../../application/usecases/google-auth.usecase';
import { ConfigService } from '@nestjs/config';
import { SendVerificationEmailUseCase } from '../../application/usecases/send-verification-email.usecase';
import { VerifyEmailUseCase } from '../../application/usecases/verify-email.usecase';
import { GetEmailVerificationStatusUseCase } from '../../application/usecases/get-email-verification-status.usecase';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly loginUseCase: LoginUseCase,
    private readonly logoutUseCase: LogoutUseCase,
    private readonly registerUseCase: RegisterUseCase,
    private readonly toggleUserStatusUseCase: ToggleUserStatusUseCase,
    private readonly googleAuthUseCase: GoogleAuthUseCase,
    private readonly verifyEmailUseCase: VerifyEmailUseCase,
    private readonly sendVerificationEmailUseCase: SendVerificationEmailUseCase,
    private readonly getEmailVerificationStatusUseCase: GetEmailVerificationStatusUseCase,
    private readonly configService: ConfigService,
  ) { }

  @Post('login')
  @ApiOperation({ summary: 'Login user' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({ status: 200, description: 'Successful login' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto) {
    return this.loginUseCase.execute(loginDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 200, description: 'Successful logout' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async logout(@Req() request: any) {
    const userId = request.user.sub;
    return this.logoutUseCase.execute(userId);
  }

  @Patch('users/:id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Activate or deactivate a user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ schema: { properties: { isActive: { type: 'boolean' } } } })
  @ApiResponse({ status: 200, description: 'User status updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async toggleUserStatus(
    @Param('id') id: number,
    @Body('isActive') isActive: boolean,
  ) {
    return this.toggleUserStatusUseCase.execute(id, isActive);
  }

  @Post('register')
  @ApiOperation({ summary: 'Register new user' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 409, description: 'User with this username already exists' })
  async register(@Body() registerDto: RegisterDto) {
    return this.registerUseCase.execute(registerDto);
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Iniciar autenticación con Google' })
  async googleAuth() {
    // Este endpoint redirige a Google para autenticación
    // No necesita implementación
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Callback de autenticación de Google' })
  async googleAuthRedirect(@Req() req, @Res() res: Response) {
    const result = await this.googleAuthUseCase.execute(req.user);

    // Redirigir al frontend con el token
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    res.redirect(`${frontendUrl}/auth/google-callback?token=${result.data.access_token}`);
  }

  @Get('verify-email')
  @ApiOperation({ summary: 'Verificar correo electrónico' })
  @ApiBody({ schema: { properties: { token: { type: 'string' } } } })
  @ApiResponse({ status: 200, description: 'Correo electrónico verificado con éxito' })
  @ApiResponse({ status: 400, description: 'Token inválido o expirado' })
  async verifyEmail(@Query('token') token: string) {
    return this.verifyEmailUseCase.execute(token);
  }

  @Post('send-verification-email')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enviar correo de verificación' })
  @ApiResponse({ status: 200, description: 'Correo de verificación enviado con éxito' })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  async sendVerificationEmail(@Req() request: any) {
    const userId = request.user.sub;
    return this.sendVerificationEmailUseCase.execute(userId);
  }

  @Get('email-verification-status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtener estado de verificación de correo' })
  @ApiResponse({ status: 200, description: 'Estado de verificación obtenido con éxito' })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  async getEmailVerificationStatus(@Req() request: any) {
    const userId = request.user.sub;
    return this.getEmailVerificationStatusUseCase.execute(userId);
  }
}
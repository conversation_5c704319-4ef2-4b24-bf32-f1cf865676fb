import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { users, persons } from '../../../../database/schemas/auth.schema';
import { eq, isNull, and } from 'drizzle-orm';
import { IUserRepository } from '../../domain/ports/user.repository.port';
import { User } from '../../domain/entities/user.entity';

@Injectable()
export class UserRepository implements IUserRepository {
  async findByUsername(username: string): Promise<User | null> {
    const result = await db
      .select()
      .from(users)
      .where(eq(users.username, username))
      .limit(1);

    if (!result.length) return null;

    const user = result[0];
    return new User(
      user.id, 
      user.username, 
      user.password,
      user.isActive,
      user.lastLoginTime!,
      user.updatedAt!,
      user.createdAt!,
      user.deletedAt!
    );
  }
  
  async updateLastLogin(userId: number): Promise<void> {
    await db
      .update(users)
      .set({ 
        lastLoginTime: new Date(),
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async updateUserStatus(userId: number, isActive: boolean): Promise<boolean> {
    const result = await db
      .update(users)
      .set({ 
        isActive,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))
      .returning();
    
    return result.length > 0;
  }

  async findById(userId: number): Promise<User | null> {
    const result = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
  
    if (!result.length) return null;
  
    const user = result[0];
    return new User(
      user.id, 
      user.username, 
      user.password,
      user.isActive,
      user.lastLoginTime!,
      user.updatedAt!,
      user.createdAt!,
      user.deletedAt!,
      user.email!,
    );
  }

  async findAll(): Promise<User[]> {
    const result = await db
      .select()
      .from(users)
      .where(isNull(users.deletedAt));
    
    return result.map(user => new User(
      user.id, 
      user.username, 
      user.password,
      user.isActive,
      user.lastLoginTime || undefined,
      user.updatedAt || undefined,
      user.createdAt,
      user.deletedAt || undefined
    ));
  }
  
  async create(userData: Partial<User>): Promise<User> {
    const { email, ...userDataWithoutEmail } = userData;
    const result = await db.insert(users).values({
      username: userData.username!,
      password: userData.password!,
      isActive: userData.isActive ?? true,
      createdAt: new Date(),
    }).returning();
  
    const newUser = result[0];
    return new User(
      newUser.id, 
      newUser.username, 
      newUser.password,
      newUser.isActive,
      newUser.lastLoginTime || undefined,
      newUser.updatedAt || undefined,
      newUser.createdAt,
      newUser.deletedAt || undefined,
      email
    );
  }
  
  async update(userId: number, userData: Partial<User>): Promise<User> {
    const updateData: any = {
      ...userData,
      updatedAt: new Date()
    };
    
    const result = await db
      .update(users)
      .set(updateData)
      .where(eq(users.id, userId))
      .returning();
    
    const updatedUser = result[0];
    return new User(
      updatedUser.id, 
      updatedUser.username, 
      updatedUser.password,
      updatedUser.isActive,
      updatedUser.lastLoginTime || undefined,
      updatedUser.updatedAt || undefined,
      updatedUser.createdAt,
      updatedUser.deletedAt || undefined
    );
  }
  
  async delete(userId: number): Promise<void> {
    await db
      .update(users)
      .set({ 
        deletedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async findByEmail(email: string): Promise<User | null> {
    // Primero buscamos la persona con ese email
    const personResult = await db
      .select()
      .from(persons)
      .where(eq(persons.email, email))
      .limit(1);
  
    if (!personResult.length) return null;
  
    // Luego buscamos el usuario asociado a esa persona
    const result = await db
      .select()
      .from(users)
      .where(eq(users.id, personResult[0].userId))
      .limit(1);
  
    if (!result.length) return null;
  
    const user = result[0];
    return new User(
      user.id, 
      user.username, 
      user.password,
      user.isActive,
      user.lastLoginTime!,
      user.updatedAt!,
      user.createdAt!,
      user.deletedAt!
    );
  }

  async setEmailVerificationToken(userId: number, token: string, expiresAt: Date): Promise<void> {
    await db
      .update(users)
      .set({ 
        emailVerificationToken: token,
        emailVerificationTokenExpires: expiresAt,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }
  
  async verifyEmail(token: string): Promise<boolean> {
    const result = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.emailVerificationToken, token),
          isNull(users.deletedAt)
        )
      );
    
    if (result.length === 0) return false;
    
    const user = result[0];
    
    // Verificar si el token ha expirado
    if (user.emailVerificationTokenExpires && user.emailVerificationTokenExpires < new Date()) {
      return false;
    }
    
    // Actualizar el usuario como verificado
    await db
      .update(users)
      .set({ 
        isEmailVerified: true,
        emailVerificationToken: null,
        emailVerificationTokenExpires: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));
    
    return true;
  }
  
  async findByVerificationToken(token: string): Promise<User | null> {
    const result = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.emailVerificationToken, token),
          isNull(users.deletedAt)
        )
      )
      .limit(1);
  
    if (!result.length) return null;
  
    const user = result[0];
    return new User(
      user.id, 
      user.username, 
      user.password,
      user.isActive,
      user.lastLoginTime!,
      user.updatedAt!,
      user.createdAt!,
      user.deletedAt!,
      user.email || undefined,
      user.isEmailVerified,
      user.emailVerificationToken || undefined,
      user.emailVerificationTokenExpires || undefined
    );
  }
  
  async getEmailVerificationStatus(userId: number): Promise<boolean> {
    const result = await db
      .select({ isEmailVerified: users.isEmailVerified })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    if (!result.length) return false;
    
    return result[0].isEmailVerified;
  }
}
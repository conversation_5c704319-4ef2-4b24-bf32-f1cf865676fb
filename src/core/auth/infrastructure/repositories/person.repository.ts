import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { persons } from '../../../../database/schemas/auth.schema';
import { eq, isNull } from 'drizzle-orm';
import { IPersonRepository } from '../../domain/ports/person.repository.port';
import { Person } from '../../domain/entities/person.entity';

@Injectable()
export class PersonRepository implements IPersonRepository {
  async findByUserId(userId: number): Promise<Person | null> {
    const result = await db
      .select()
      .from(persons)
      .where(eq(persons.userId, userId))
      .limit(1);

    if (!result.length) return null;

    const person = result[0];
    return this.mapToDomain(person);
  }

  async findById(personId: number): Promise<Person | null> {
    const result = await db
      .select()
      .from(persons)
      .where(eq(persons.id, personId))
      .limit(1);

    if (!result.length) return null;

    const person = result[0];
    return this.mapToDomain(person);
  }

  async findAll(): Promise<Person[]> {
    const result = await db
      .select()
      .from(persons)
      .where(isNull(persons.deletedAt));
    
    return result.map(person => this.mapToDomain(person));
  }

  async create(personData: Partial<Person>): Promise<Person> {
    const result = await db.insert(persons).values({
      userId: personData.userId!,
      firstName: personData.firstName!,
      lastName: personData.lastName!,
      middleName: personData.middleName,
      email: personData.email,
      alternativeEmail: personData.alternativeEmail,
      phoneNumber: personData.phoneNumber,
      mobileNumber: personData.mobileNumber,
      address: personData.address,
      gender: personData.gender,
      birthDate: personData.birthDate,
      documentNumber: personData.documentNumber,
      documentType: personData.documentType,
      createdAt: new Date(),
    }).returning();

    const newPerson = result[0];
    return this.mapToDomain(newPerson);
  }

  async update(personId: number, personData: Partial<Person>): Promise<Person> {
    const updateData: any = {
      ...personData,
      updatedAt: new Date()
    };
    
    const result = await db
      .update(persons)
      .set(updateData)
      .where(eq(persons.id, personId))
      .returning();
    
    const updatedPerson = result[0];
    return this.mapToDomain(updatedPerson);
  }

  async delete(personId: number): Promise<void> {
    await db
      .update(persons)
      .set({ 
        deletedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(persons.id, personId));
  }

  private mapToDomain(person: any): Person {
    return new Person(
      person.id,
      person.userId,
      person.firstName,
      person.lastName,
      person.middleName || undefined,
      person.email || undefined,
      person.alternativeEmail || undefined,
      person.phoneNumber || undefined,
      person.mobileNumber || undefined,
      person.address || undefined,
      person.gender || undefined,
      person.birthDate || undefined,
      person.documentNumber || undefined,
      person.documentType || undefined,
      person.updatedAt || undefined,
      person.createdAt,
      person.deletedAt || undefined
    );
  }
}
import { Injectable } from '@nestjs/common';
import { db } from '../../../../database';
import { tokens } from '../../../../database/schemas/auth.schema';
import { eq, and, lt } from 'drizzle-orm';
import { ITokenRepository, TokenData } from '../../domain/ports/token.repository.port';

@Injectable()
export class TokenRepository implements ITokenRepository {
  async save(tokenData: TokenData): Promise<TokenData> {
    const result = await db.insert(tokens).values({
      userId: tokenData.userId,
      token: tokenData.token,
      isRevoked: tokenData.isRevoked,
      expiresAt: tokenData.expiresAt,
    }).returning();
    
    return result[0];
  }

  async findByToken(token: string): Promise<TokenData | null> {
    const result = await db
      .select()
      .from(tokens)
      .where(eq(tokens.token, token))
      .limit(1);

    if (!result.length) return null;
    
    return {
      id: result[0].id,
      userId: result[0].userId,
      token: result[0].token,
      isRevoked: result[0].isRevoked,
      expiresAt: result[0].expiresAt,
      createdAt: result[0].createdAt,
    };
  }

  async revokeToken(token: string): Promise<void> {
    await db
      .update(tokens)
      .set({ isRevoked: true })
      .where(eq(tokens.token, token));
  }

  async revokeAllUserTokens(userId: number): Promise<void> {
    await db
      .update(tokens)
      .set({ isRevoked: true })
      .where(eq(tokens.userId, userId));
  }

  async cleanupExpiredTokens(): Promise<void> {
    const now = new Date();
    await db
      .delete(tokens)
      .where(
        and(
          lt(tokens.expiresAt, now),
          eq(tokens.isRevoked, true)
        )
      );
  }
}
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';

@Injectable()
export class GetEmailVerificationStatusUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute(userId: number) {
    // Verificar que el usuario exista
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Obtener el estado de verificación
    const isVerified = await this.userRepository.getEmailVerificationStatus(userId);
    
    return {
      message: 'Email verification status retrieved successfully',
      data: {
        isEmailVerified: isVerified,
      },
    };
  }
}
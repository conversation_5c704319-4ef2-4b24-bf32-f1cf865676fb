import { Inject, Injectable } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';
import { IPersonRepository } from '../../domain/ports/person.repository.port';
import { ITokenService } from '../../domain/ports/token.service.port';
import { IRoleRepository } from '../../../rbac/domain/ports/role.repository.port';
import { IPasswordHasher } from '../../domain/ports/password.hasher.port';
import { randomBytes } from 'crypto';
import { IUserRoleRepository } from '@core/rbac/domain/ports/user-role-repository.port';

@Injectable()
export class GoogleAuthUseCase {
    constructor(
        @Inject('IUserRepository')
        private readonly userRepository: IUserRepository,
        @Inject('IPersonRepository')
        private readonly personRepository: IPersonRepository,
        @Inject('ITokenService')
        private readonly tokenService: ITokenService,
        @Inject('IRoleRepository')
        private readonly roleRepository: IRoleRepository,
        @Inject('IUserRoleRepository')
        private readonly userRoleRepository: IUserRoleRepository,
        @Inject('IPasswordHasher')
        private readonly passwordHasher: IPasswordHasher,
    ) { }

    async execute(googleUser: any) {
        // Buscar si el usuario ya existe por email
        let user = await this.userRepository.findByEmail(googleUser.email);

        // Dentro del método execute
        if (!user) {
            // Si no existe, crear un nuevo usuario
            // Generar un nombre de usuario basado en el email
            const username = googleUser.email.split('@')[0];

            // Generar una contraseña aleatoria (el usuario nunca la usará)
            const randomPassword = randomBytes(16).toString('hex');
            const hashedPassword = await this.passwordHasher.hash(randomPassword);

            // Crear el usuario (sin pasar el email directamente)
            user = await this.userRepository.create({
                username,
                password: hashedPassword,
                isActive: true,
            });

            // Crear la información personal
            await this.personRepository.create({
                userId: user.id,
                firstName: googleUser.firstName,
                lastName: googleUser.lastName,
                email: googleUser.email,
            });

            // Asignar el rol de usuario
            const userRole = await this.roleRepository.findByName('user');
            if (userRole) {
                await this.userRoleRepository.create({
                    userId: user.id,
                    roleId: userRole.id,
                });
            }
        }

        // Actualizar el último tiempo de inicio de sesión
        await this.userRepository.updateLastLogin(user.id);

        // Generar token
        const payload = {
            sub: user.id,
            username: user.username,
        };

        const token = await this.tokenService.generate(payload);

        return {
            message: 'Google authentication successful',
            data: {
                access_token: token,
                user: {
                    id: user.id,
                    username: user.username,
                    isActive: user.isActive,
                    lastLoginTime: user.lastLoginTime,
                    createdAt: user.createdAt
                },
            },
        };
    }
}
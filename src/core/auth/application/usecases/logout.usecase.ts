import { Inject, Injectable } from '@nestjs/common';
import { ITokenService } from '../../domain/ports/token.service.port';

@Injectable()
export class LogoutUseCase {
  constructor(
    @Inject('ITokenService')
    private readonly tokenService: ITokenService,
  ) {}

  async execute(userId: number) {
    await this.tokenService.invalidateUserSessions(userId);
    return {
      message: 'Logout successful',
      data: null,
    };
  }
}
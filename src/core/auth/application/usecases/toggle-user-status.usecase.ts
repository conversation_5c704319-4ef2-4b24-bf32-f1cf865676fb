import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';

@Injectable()
export class ToggleUserStatusUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute(userId: number, isActive: boolean) {
    const updated = await this.userRepository.updateUserStatus(userId, isActive);
    
    if (!updated) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    return {
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { userId, isActive },
    };
  }
}
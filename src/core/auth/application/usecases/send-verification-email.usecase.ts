import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';
import { IEmailService } from '../../domain/ports/email.service.port';
import { randomBytes } from 'crypto';

@Injectable()
export class SendVerificationEmailUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IEmailService')
    private readonly emailService: IEmailService,
  ) {}

  async execute(userId: number) {
    // Buscar el usuario
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        data: null,
      });
    }

    // Verificar que el usuario tenga un correo electrónico
    if (!user.email) {
      throw new NotFoundException({
        message: 'User does not have an email address',
        data: null,
      });
    }

    // Generar un token aleatorio
    const token = randomBytes(32).toString('hex');
    
    // Establecer la fecha de expiración (24 horas)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    
    // Guardar el token en la base de datos
    await this.userRepository.setEmailVerificationToken(user.id, token, expiresAt);
    
    // Enviar el correo electrónico
    await this.emailService.sendVerificationEmail(user.email, token);
    
    return {
      message: 'Verification email sent successfully',
      data: null,
    };
  }
}
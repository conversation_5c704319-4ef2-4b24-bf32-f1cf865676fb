import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { LoginDto } from '../dtos/login.dto';
import { IUserRepository } from '../../domain/ports/user.repository.port';
import { IPasswordHasher } from '../../domain/ports/password.hasher.port';
import { ITokenService, TokenPayload } from '../../domain/ports/token.service.port';

@Injectable()
export class LoginUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IPasswordHasher')
    private readonly passwordHasher: IPasswordHasher,
    @Inject('ITokenService')
    private readonly tokenService: ITokenService,
  ) {}

  async execute(loginDto: LoginDto) {
    const user = await this.userRepository.findByUsername(loginDto.username);
    if (!user) {
      throw new UnauthorizedException({
        message: 'Invalid credentials',
        data: null,
      });
    }

    // Verificar si el usuario está activo
    if (!user.isActive) {
      throw new UnauthorizedException({
        message: 'User account is inactive',
        data: null,
      });
    }

    const isPasswordValid = await this.passwordHasher.compare(
      loginDto.password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException({
        message: 'Invalid credentials',
        data: null,
      });
    }

    // Actualizar el último tiempo de inicio de sesión
    await this.userRepository.updateLastLogin(user.id);

    const payload: TokenPayload = {
      sub: user.id,
      username: user.username,
    };

    const token = await this.tokenService.generate(payload);

    return {
      message: 'Login successful',
      data: {
        access_token: token,
        user: {
          id: user.id,
          username: user.username,
          isActive: user.isActive,
          lastLoginTime: user.lastLoginTime,
          createdAt: user.createdAt
        },
      },
    };
  }
}
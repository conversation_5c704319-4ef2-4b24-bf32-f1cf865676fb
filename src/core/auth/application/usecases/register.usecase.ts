import { Inject, Injectable, ConflictException } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';
import { IPasswordHasher } from '../../domain/ports/password.hasher.port';
import { IPersonRepository } from '../../domain/ports/person.repository.port';
import { RegisterDto } from '../dtos/register.dto';
import { IRoleRepository } from '../../../rbac/domain/ports/role.repository.port';
import { IUserRoleRepository } from '@rbac/domain/ports/user-role-repository.port';
import { IEmailService } from '../../domain/ports/email.service.port';
import { randomBytes } from 'crypto';

@Injectable()
export class RegisterUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
    @Inject('IPasswordHasher')
    private readonly passwordHasher: IPasswordHasher,
    @Inject('IPersonRepository')
    private readonly personRepository: IPersonRepository,
    @Inject('IRoleRepository')
    private readonly roleRepository: IRoleRepository,
    @Inject('IUserRoleRepository')
    private readonly userRoleRepository: IUserRoleRepository,
    @Inject('IEmailService')
    private readonly emailService: IEmailService,
  ) {}

  async execute(registerDto: RegisterDto) {
    // Verificar si ya existe un usuario con el mismo nombre
    const existingUser = await this.userRepository.findByUsername(registerDto.username);
    if (existingUser) {
      throw new ConflictException({
        message: 'User with this username already exists',
        data: null,
      });
    }

    // Verificar si ya existe un usuario con el mismo correo
    if (registerDto.person.email) {
      const existingEmail = await this.userRepository.findByEmail(registerDto.person.email);
      if (existingEmail) {
        throw new ConflictException({
          message: 'User with this email already exists',
          data: null,
        });
      }
    }

    // Hashear la contraseña
    const hashedPassword = await this.passwordHasher.hash(registerDto.password);

    // Crear el usuario
    const user = await this.userRepository.create({
      username: registerDto.username,
      password: hashedPassword,
      isActive: true,
      email: registerDto.person.email,
      isEmailVerified: false,
    });

    // Crear la información personal asociada al usuario
    const person = await this.personRepository.create({
      userId: user.id,
      firstName: registerDto.person.firstName,
      lastName: registerDto.person.lastName,
      email: registerDto.person.email,
      phoneNumber: registerDto.person.phoneNumber,
    });

    // Asignar el rol de usuario por defecto
    const userRole = await this.roleRepository.findByName('user');
    if (userRole) {
      await this.userRoleRepository.create({
        userId: user.id,
        roleId: userRole.id,
      });
    }

    // Enviar correo de verificación si se proporcionó un correo electrónico
    if (registerDto.person.email) {
      // Generar un token aleatorio
      const token = randomBytes(32).toString('hex');
      
      // Establecer la fecha de expiración (24 horas)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      
      // Guardar el token en la base de datos
      await this.userRepository.setEmailVerificationToken(user.id, token, expiresAt);
      
      // Enviar el correo electrónico
      await this.emailService.sendVerificationEmail(registerDto.person.email, token);
    }

    return {
      message: 'Registration successful',
      data: {
        id: user.id,
        username: user.username,
        isActive: user.isActive,
        person: {
          firstName: person.firstName,
          lastName: person.lastName,
          email: person.email,
        }
      },
    };
  }
}
import { Inject, Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { IUserRepository } from '../../domain/ports/user.repository.port';

@Injectable()
export class VerifyEmailUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}

  async execute(token: string) {
    // Verificar que el token exista
    const user = await this.userRepository.findByVerificationToken(token);
    if (!user) {
      throw new NotFoundException({
        message: 'Invalid or expired verification token',
        data: null,
      });
    }

    // Verificar que el token no haya expirado
    if (user.emailVerificationTokenExpires && user.emailVerificationTokenExpires < new Date()) {
      throw new BadRequestException({
        message: 'Verification token has expired',
        data: null,
      });
    }

    // Verificar el correo electrónico
    const success = await this.userRepository.verifyEmail(token);
    
    if (!success) {
      throw new BadRequestException({
        message: 'Failed to verify email',
        data: null,
      });
    }
    
    return {
      message: 'Email verified successfully',
      data: null,
    };
  }
}
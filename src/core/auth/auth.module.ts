import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthController } from './infrastructure/controllers/auth.controller';
import { LoginUseCase } from './application/usecases/login.usecase';
import { LogoutUseCase } from './application/usecases/logout.usecase';
import { UserRepository } from './infrastructure/repositories/user.repository';
import { PersonRepository } from './infrastructure/repositories/person.repository';
import { BcryptService } from './infrastructure/services/bcrypt.service';
import { JwtService } from './infrastructure/services/jwt.service';
import { TokenRepository } from './infrastructure/repositories/token.repository';
import { AuthGuard } from './infrastructure/guards/auth.guard';
import { ToggleUserStatusUseCase } from './application/usecases/toggle-user-status.usecase';
import { RbacModule } from '../rbac/rbac.module';
import { RegisterUseCase } from './application/usecases/register.usecase';
import { PassportModule } from '@nestjs/passport';
import { GoogleStrategy } from './infrastructure/strategies/google.strategy';
import { GoogleAuthUseCase } from './application/usecases/google-auth.usecase';
import { SendVerificationEmailUseCase } from './application/usecases/send-verification-email.usecase';
import { VerifyEmailUseCase } from './application/usecases/verify-email.usecase';
import { GetEmailVerificationStatusUseCase } from './application/usecases/get-email-verification-status.usecase';
import { NodemailerService } from './infrastructure/services/nodemailer.service';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '1d' },
      }),
    }),
    RbacModule,
  ],
  controllers: [AuthController],
  providers: [
    LoginUseCase,
    LogoutUseCase,
    RegisterUseCase,
    ToggleUserStatusUseCase,
    GoogleAuthUseCase,
    SendVerificationEmailUseCase,
    VerifyEmailUseCase,
    GetEmailVerificationStatusUseCase,
    GoogleStrategy,
    AuthGuard,
    {
      provide: 'IUserRepository',
      useClass: UserRepository,
    },
    {
      provide: 'IPersonRepository',
      useClass: PersonRepository,
    },
    {
      provide: 'IPasswordHasher',
      useClass: BcryptService,
    },
    {
      provide: 'ITokenService',
      useClass: JwtService,
    },
    {
      provide: 'ITokenRepository',
      useClass: TokenRepository,
    },
    {
      provide: 'IEmailService',
      useClass: NodemailerService,
    }
  ],
  exports: [
    'IUserRepository',
    'IPersonRepository',
    'IPasswordHasher',
    'ITokenService',
    'ITokenRepository',
    'IEmailService',
    AuthGuard,
  ],
})
export class AuthModule {}
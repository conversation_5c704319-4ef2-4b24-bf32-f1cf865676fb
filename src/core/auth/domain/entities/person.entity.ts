export class Person {
    constructor(
      public readonly id: number,
      public readonly userId: number,
      public readonly firstName: string,
      public readonly lastName: string,
      public readonly middleName?: string,
      public readonly email?: string,
      public readonly alternativeEmail?: string,
      public readonly phoneNumber?: string,
      public readonly mobileNumber?: string,
      public readonly address?: string,
      public readonly gender?: string,
      public readonly birthDate?: Date,
      public readonly documentNumber?: string,
      public readonly documentType?: string,
      public readonly updatedAt?: Date,
      public readonly createdAt?: Date,
      public readonly deletedAt?: Date,
    ) {}
  }
export interface TokenData {
    id?: number;
    userId: number;
    token: string;
    isRevoked: boolean;
    expiresAt: Date;
    createdAt?: Date;
  }
  
  export interface ITokenRepository {
    save(tokenData: TokenData): Promise<TokenData>;
    findByToken(token: string): Promise<TokenData | null>;
    revokeToken(token: string): Promise<void>;
    revokeAllUserTokens(userId: number): Promise<void>;
    cleanupExpiredTokens(): Promise<void>;
  }
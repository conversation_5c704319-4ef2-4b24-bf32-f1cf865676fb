import { Person } from '../entities/person.entity';

export interface IPersonRepository {
  findByUserId(userId: number): Promise<Person | null>;
  findById(personId: number): Promise<Person | null>;
  findAll(): Promise<Person[]>;
  create(personData: Partial<Person>): Promise<Person>;
  update(personId: number, personData: Partial<Person>): Promise<Person>;
  delete(personId: number): Promise<void>;
}
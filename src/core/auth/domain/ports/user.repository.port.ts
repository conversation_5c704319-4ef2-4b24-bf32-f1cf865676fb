import { User } from '../entities/user.entity';

export interface IUserRepository {
  findByUsername(username: string): Promise<User | null>;
  findById(userId: number): Promise<User | null>;
  findAll(): Promise<User[]>;
  create(userData: Partial<User>): Promise<User>;
  update(userId: number, userData: Partial<User>): Promise<User>;
  delete(userId: number): Promise<void>;
  updateLastLogin(userId: number): Promise<void>;
  updateUserStatus(userId: number, isActive: boolean): Promise<boolean>;
  findByEmail(email: string): Promise<User | null>;
  setEmailVerificationToken(userId: number, token: string, expiresAt: Date): Promise<void>;
  verifyEmail(token: string): Promise<boolean>;
  findByVerificationToken(token: string): Promise<User | null>;
  getEmailVerificationStatus(userId: number): Promise<boolean>;
}
import {
    Injectable,
    NestInterceptor,
    ExecutionContext,
    CallHandler,
    HttpStatus,
  } from '@nestjs/common';
  import { Observable } from 'rxjs';
  import { map } from 'rxjs/operators';
  import { ApiResponse } from '../interfaces/api-response.interface';
  
  @Injectable()
  export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
    intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
      const ctx = context.switchToHttp();
      const response = ctx.getResponse();
      const statusCode = response.statusCode || HttpStatus.OK;
  
      return next.handle().pipe(
        map((data) => {
          // Si la respuesta ya tiene el formato correcto, la devolvemos tal cual
          if (data && data.statusCode !== undefined && data.message !== undefined && data.data !== undefined) {
            return data;
          }
  
          // Si la respuesta tiene un mensaje pero no datos
          if (data && data.message && !data.data) {
            return {
              message: data.message,
              data: null,
              statusCode: statusCode,
            };
          }
  
          // Respuesta estándar
          return {
            message: data.message || 'Success',
            data: data.data || data,
            statusCode: statusCode,
          };
        }),
      );
    }
  }
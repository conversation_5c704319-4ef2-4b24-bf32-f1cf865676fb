APP_NAME=API
APP_PORT=3000
APP_LIMIT_SIZE_FILE=10mb

TZ=America/La_Paz

JWT_SECRET = "your_jwt_secret_key"

#DataBase
DATABASE_URL=postgres://postgres:postgres@localhost:5435/postgres


# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=abosqcvmezpxjfyt
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=r2Speak
FRONTEND_URL=http://localhost:3000


# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
languages {
  id SERIAL PRIMARY KEY,
  code VA<PERSON>HAR(10) NOT NULL UNIQUE,
  name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
  nativeName VARCHAR(100) NOT NULL,
  flagUrl VARCHAR(255),
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

categories {
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  iconUrl VARCHAR(255),
  order INTEGER DEFAULT 0,
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

subcategories {
  id SERIAL PRIMARY KEY,
  categoryId INTEGER NOT NULL REFERENCES categories(id),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  iconUrl VARCHAR(255),
  order INTEGER DEFAULT 0,
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

words {
  id SERIAL PRIMARY KEY,
  subcategoryId INTEGER NOT NULL REFERENCES subcategories(id),
  languageId INTEGER NOT NULL REFERENCES languages(id),
  originalWord VARCHAR(255) NOT NULL,
  meaning TEXT NOT NULL,
  example TEXT,
  audioUrl VARCHAR(255),
  imageUrl VARCHAR(255),
  difficultyLevel SMALLINT DEFAULT 1 NOT NULL,
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

exercises {
  id SERIAL PRIMARY KEY,
  wordId INTEGER NOT NULL REFERENCES words(id),
  type ENUM('multiple_choice', 'fill_blank', 'listening', 'speaking', 'matching', 'translation') NOT NULL,
  question TEXT NOT NULL,
  correctAnswer TEXT NOT NULL,
  options TEXT,
  hint TEXT,
  points INTEGER DEFAULT 10 NOT NULL,
  timeLimit INTEGER,
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

learner_profiles {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL UNIQUE REFERENCES users(id),
  firstName VARCHAR(100),
  lastName VARCHAR(100),
  profilePicture VARCHAR(255),
  currentLevel INTEGER DEFAULT 1 NOT NULL,
  totalXp INTEGER DEFAULT 0 NOT NULL,
  streak INTEGER DEFAULT 0 NOT NULL,
  lastActiveDate TIMESTAMP DEFAULT NOW() NOT NULL,
  preferredLanguageId INTEGER REFERENCES languages(id),
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

user_progress {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL REFERENCES users(id),
  wordId INTEGER NOT NULL REFERENCES words(id),
  learnedPercentage REAL DEFAULT 0 NOT NULL,
  correctAttempts INTEGER DEFAULT 0 NOT NULL,
  totalAttempts INTEGER DEFAULT 0 NOT NULL,
  lastReviewDate TIMESTAMP DEFAULT NOW() NOT NULL,
  nextReviewDate TIMESTAMP,
  strengthLevel SMALLINT DEFAULT 0 NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

achievements {
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  iconUrl VARCHAR(255),
  requiredCondition TEXT NOT NULL,
  xpReward INTEGER DEFAULT 0 NOT NULL,
  isActive BOOLEAN DEFAULT TRUE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

user_achievements {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL REFERENCES users(id),
  achievementId INTEGER NOT NULL REFERENCES achievements(id),
  unlockedAt TIMESTAMP DEFAULT NOW() NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}
user_settings {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL UNIQUE REFERENCES users(id),
  dailyGoal INTEGER DEFAULT 50 NOT NULL,
  notificationsEnabled BOOLEAN DEFAULT TRUE NOT NULL,
  soundEnabled BOOLEAN DEFAULT TRUE NOT NULL,
  darkMode BOOLEAN DEFAULT FALSE NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}

practice_sessions {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL REFERENCES users(id),
  subcategoryId INTEGER REFERENCES subcategories(id),
  startTime TIMESTAMP DEFAULT NOW() NOT NULL,
  endTime TIMESTAMP,
  totalXpEarned INTEGER DEFAULT 0 NOT NULL,
  correctAnswers INTEGER DEFAULT 0 NOT NULL,
  totalQuestions INTEGER DEFAULT 0 NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}


attempt_history {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL REFERENCES users(id),
  exerciseId INTEGER NOT NULL REFERENCES exercises(id),
  userResponse TEXT NOT NULL,
  isCorrect BOOLEAN DEFAULT FALSE NOT NULL,
  timeTaken INTEGER NOT NULL,
  xpEarned INTEGER DEFAULT 0 NOT NULL,
  attemptDate TIMESTAMP DEFAULT NOW() NOT NULL,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}


daily_goals {
  id SERIAL PRIMARY KEY,
  userId INTEGER NOT NULL REFERENCES users(id),
  date TIMESTAMP NOT NULL,
  targetXp INTEGER DEFAULT 50 NOT NULL,
  achievedXp INTEGER DEFAULT 0 NOT NULL,
  completed BOOLEAN DEFAULT FALSE NOT NULL,
  completedAt TIMESTAMP,
  createdAt TIMESTAMP DEFAULT NOW() NOT NULL,
  updatedAt TIMESTAMP ON UPDATE NOW(),
  deletedAt TIMESTAMP
}
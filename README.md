# NestJS Starter Repository

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment
npm run generate

npm run migrate


Para pruebas unitarias: npm run test
Para pruebas e2e: npm run test:e2e
Para ver la cobertura de pruebas: npm run test:cov
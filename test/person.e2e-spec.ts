import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';

describe('PersonController (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Obtener el servicio JWT para generar un token para las pruebas
    jwtService = moduleFixture.get<JwtService>(JwtService);
    
    // Crear un token de prueba
    authToken = jwtService.sign({ 
      sub: 1, // ID del usuario admin
      username: 'admin',
      roles: ['admin']
    });
  });

  afterAll(async () => {
    await app.close();
  });

  it('/persons/user/1 (GET) - should get person by user ID', () => {
    return request(app.getHttpServer())
      .get('/persons/user/1')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200)
      .expect(res => {
        expect(res.body).toHaveProperty('message');
        expect(res.body).toHaveProperty('data');
        expect(res.body.data).toHaveProperty('firstName');
        expect(res.body.data).toHaveProperty('lastName');
        expect(res.body.data).toHaveProperty('email');
      });
  });

  it('/persons/1 (GET) - should get person by ID', () => {
    return request(app.getHttpServer())
      .get('/persons/1')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200)
      .expect(res => {
        expect(res.body).toHaveProperty('message');
        expect(res.body).toHaveProperty('data');
        expect(res.body.data).toHaveProperty('firstName');
        expect(res.body.data).toHaveProperty('lastName');
        expect(res.body.data).toHaveProperty('email');
      });
  });

  // Esta prueba asume que el usuario con ID 999 no existe
  it('/persons/user/999 (GET) - should return 404 for non-existent user', () => {
    return request(app.getHttpServer())
      .get('/persons/user/999')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(404);
  });
});
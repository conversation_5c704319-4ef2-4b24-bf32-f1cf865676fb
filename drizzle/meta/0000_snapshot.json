{"id": "2a23e57d-9bbf-4dc2-ab00-7d5674317821", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "permissions_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "resource": {"name": "resource", "type": "text", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permissions_name_unique": {"name": "permissions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.persons": {"name": "persons", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "persons_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "alternative_email": {"name": "alternative_email", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "mobile_number": {"name": "mobile_number", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"persons_user_id_users_id_fk": {"name": "persons_user_id_users_id_fk", "tableFrom": "persons", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "role_permissions_permission_id_permissions_id_fk": {"name": "role_permissions_permission_id_permissions_id_fk", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"role_permissions_role_id_permission_id_pk": {"name": "role_permissions_role_id_permission_id_pk", "columns": ["role_id", "permission_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "roles_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"roles_name_unique": {"name": "roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tokens": {"name": "tokens", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "tokens_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tokens_user_id_users_id_fk": {"name": "tokens_user_id_users_id_fk", "tableFrom": "tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_role_id_roles_id_fk": {"name": "user_roles_role_id_roles_id_fk", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_roles_user_id_role_id_pk": {"name": "user_roles_user_id_role_id_pk", "columns": ["user_id", "role_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "users_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "text", "primaryKey": false, "notNull": false}, "email_verification_token_expires": {"name": "email_verification_token_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_time": {"name": "last_login_time", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.achievements": {"name": "achievements", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "required_condition": {"name": "required_condition", "type": "text", "primaryKey": false, "notNull": true}, "xp_reward": {"name": "xp_reward", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.attempt_history": {"name": "attempt_history", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "exercise_id": {"name": "exercise_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_response": {"name": "user_response", "type": "text", "primaryKey": false, "notNull": true}, "is_correct": {"name": "is_correct", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "time_taken": {"name": "time_taken", "type": "integer", "primaryKey": false, "notNull": true}, "xp_earned": {"name": "xp_earned", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "attempt_date": {"name": "attempt_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"attempt_history_user_id_users_id_fk": {"name": "attempt_history_user_id_users_id_fk", "tableFrom": "attempt_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "attempt_history_exercise_id_exercises_id_fk": {"name": "attempt_history_exercise_id_exercises_id_fk", "tableFrom": "attempt_history", "tableTo": "exercises", "schemaTo": "learning", "columnsFrom": ["exercise_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.categories": {"name": "categories", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.daily_goals": {"name": "daily_goals", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "target_xp": {"name": "target_xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "achieved_xp": {"name": "achieved_xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"daily_goals_user_id_users_id_fk": {"name": "daily_goals_user_id_users_id_fk", "tableFrom": "daily_goals", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.exercises": {"name": "exercises", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "word_id": {"name": "word_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "exercise_type", "typeSchema": "learning", "primaryKey": false, "notNull": true}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true}, "correct_answer": {"name": "correct_answer", "type": "text", "primaryKey": false, "notNull": true}, "options": {"name": "options", "type": "text", "primaryKey": false, "notNull": false}, "hint": {"name": "hint", "type": "text", "primaryKey": false, "notNull": false}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"exercises_word_id_words_id_fk": {"name": "exercises_word_id_words_id_fk", "tableFrom": "exercises", "tableTo": "words", "schemaTo": "learning", "columnsFrom": ["word_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.languages": {"name": "languages", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "native_name": {"name": "native_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "flag_url": {"name": "flag_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"languages_code_unique": {"name": "languages_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.learner_profiles": {"name": "learner_profiles", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "profile_picture": {"name": "profile_picture", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "current_level": {"name": "current_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "total_xp": {"name": "total_xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "streak": {"name": "streak", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_active_date": {"name": "last_active_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "preferred_language_id": {"name": "preferred_language_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"learner_profiles_user_id_users_id_fk": {"name": "learner_profiles_user_id_users_id_fk", "tableFrom": "learner_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "learner_profiles_preferred_language_id_languages_id_fk": {"name": "learner_profiles_preferred_language_id_languages_id_fk", "tableFrom": "learner_profiles", "tableTo": "languages", "schemaTo": "learning", "columnsFrom": ["preferred_language_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"learner_profiles_user_id_unique": {"name": "learner_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.levels": {"name": "levels", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "required_xp": {"name": "required_xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.practice_sessions": {"name": "practice_sessions", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "subcategory_id": {"name": "subcategory_id", "type": "integer", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "total_xp_earned": {"name": "total_xp_earned", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "correct_answers": {"name": "correct_answers", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_questions": {"name": "total_questions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"practice_sessions_user_id_users_id_fk": {"name": "practice_sessions_user_id_users_id_fk", "tableFrom": "practice_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "practice_sessions_subcategory_id_subcategories_id_fk": {"name": "practice_sessions_subcategory_id_subcategories_id_fk", "tableFrom": "practice_sessions", "tableTo": "subcategories", "schemaTo": "learning", "columnsFrom": ["subcategory_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.subcategories": {"name": "subcategories", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon_url": {"name": "icon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subcategories_category_id_categories_id_fk": {"name": "subcategories_category_id_categories_id_fk", "tableFrom": "subcategories", "tableTo": "categories", "schemaTo": "learning", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.user_achievements": {"name": "user_achievements", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "achievement_id": {"name": "achievement_id", "type": "integer", "primaryKey": false, "notNull": true}, "unlocked_at": {"name": "unlocked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_achievements_user_id_users_id_fk": {"name": "user_achievements_user_id_users_id_fk", "tableFrom": "user_achievements", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_achievements_achievement_id_achievements_id_fk": {"name": "user_achievements_achievement_id_achievements_id_fk", "tableFrom": "user_achievements", "tableTo": "achievements", "schemaTo": "learning", "columnsFrom": ["achievement_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.user_progress": {"name": "user_progress", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "word_id": {"name": "word_id", "type": "integer", "primaryKey": false, "notNull": true}, "learned_percentage": {"name": "learned_percentage", "type": "real", "primaryKey": false, "notNull": true, "default": 0}, "correct_attempts": {"name": "correct_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_attempts": {"name": "total_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_review_date": {"name": "last_review_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "next_review_date": {"name": "next_review_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "strength_level": {"name": "strength_level", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_progress_user_id_users_id_fk": {"name": "user_progress_user_id_users_id_fk", "tableFrom": "user_progress", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_progress_word_id_words_id_fk": {"name": "user_progress_word_id_words_id_fk", "tableFrom": "user_progress", "tableTo": "words", "schemaTo": "learning", "columnsFrom": ["word_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.user_settings": {"name": "user_settings", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "daily_goal": {"name": "daily_goal", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "notifications_enabled": {"name": "notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sound_enabled": {"name": "sound_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "dark_mode": {"name": "dark_mode", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "learning.words": {"name": "words", "schema": "learning", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "subcategory_id": {"name": "subcategory_id", "type": "integer", "primaryKey": false, "notNull": true}, "language_id": {"name": "language_id", "type": "integer", "primaryKey": false, "notNull": true}, "original_word": {"name": "original_word", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "meaning": {"name": "meaning", "type": "text", "primaryKey": false, "notNull": true}, "example": {"name": "example", "type": "text", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "difficulty_level": {"name": "difficulty_level", "type": "smallint", "primaryKey": false, "notNull": true, "default": 1}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"words_subcategory_id_subcategories_id_fk": {"name": "words_subcategory_id_subcategories_id_fk", "tableFrom": "words", "tableTo": "subcategories", "schemaTo": "learning", "columnsFrom": ["subcategory_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "words_language_id_languages_id_fk": {"name": "words_language_id_languages_id_fk", "tableFrom": "words", "tableTo": "languages", "schemaTo": "learning", "columnsFrom": ["language_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"learning.difficulty_level": {"name": "difficulty_level", "schema": "learning", "values": ["beginner", "elementary", "intermediate", "advanced", "proficient"]}, "learning.exercise_type": {"name": "exercise_type", "schema": "learning", "values": ["multiple_choice", "fill_blank", "listening", "speaking", "matching", "translation"]}}, "schemas": {"learning": "learning"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}